1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.my_flutter_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:5:5-67
15-->/Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:5:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->/Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:47:5-52:15
24        <intent>
24-->/Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:48:9-51:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->/Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:49:13-72
25-->/Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:49:21-70
26
27            <data android:mimeType="text/plain" />
27-->/Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:50:13-50
27-->/Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:50:19-48
28        </intent>
29    </queries>
30
31    <permission
31-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
32        android:name="com.example.my_flutter_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
32-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
33        android:protectionLevel="signature" />
33-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
34
35    <uses-permission android:name="com.example.my_flutter_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
35-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
35-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
36
37    <application
38        android:name="android.app.Application"
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
40        android:debuggable="true"
41        android:extractNativeLibs="true"
42        android:icon="@mipmap/ic_launcher"
43        android:label="my_flutter_app"
44        android:networkSecurityConfig="@xml/network_security_config"
45        android:requestLegacyExternalStorage="true"
46        android:usesCleartextTraffic="true" >
47        <activity
48            android:name="com.example.my_flutter_app.MainActivity"
49            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
50            android:exported="true"
51            android:hardwareAccelerated="true"
52            android:launchMode="singleTop"
53            android:taskAffinity=""
54            android:theme="@style/LaunchTheme"
55            android:windowSoftInputMode="adjustResize" >
56
57            <!--
58                 Specifies an Android theme to apply to this Activity as soon as
59                 the Android process has started. This theme is visible to the user
60                 while the Flutter UI initializes. After that, this theme continues
61                 to determine the Window background behind the Flutter UI.
62            -->
63            <meta-data
64                android:name="io.flutter.embedding.android.NormalTheme"
65                android:resource="@style/NormalTheme" />
66
67            <intent-filter>
68                <action android:name="android.intent.action.MAIN" />
69
70                <category android:name="android.intent.category.LAUNCHER" />
71            </intent-filter>
72        </activity>
73        <!--
74             Don't delete the meta-data below.
75             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
76        -->
77        <meta-data
78            android:name="flutterEmbedding"
79            android:value="2" />
80
81        <uses-library
81-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
82            android:name="androidx.window.extensions"
82-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
83            android:required="false" />
83-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
84        <uses-library
84-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
85            android:name="androidx.window.sidecar"
85-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
86            android:required="false" />
86-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
87
88        <provider
88-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
89            android:name="androidx.startup.InitializationProvider"
89-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
90            android:authorities="com.example.my_flutter_app.androidx-startup"
90-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
91            android:exported="false" >
91-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
92            <meta-data
92-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
93                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
93-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
94                android:value="androidx.startup" />
94-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
95            <meta-data
95-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
96                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
96-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
97                android:value="androidx.startup" />
97-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
98        </provider>
99
100        <receiver
100-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
101            android:name="androidx.profileinstaller.ProfileInstallReceiver"
101-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
102            android:directBootAware="false"
102-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
103            android:enabled="true"
103-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
104            android:exported="true"
104-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
105            android:permission="android.permission.DUMP" >
105-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
106            <intent-filter>
106-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
107                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
107-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
107-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
108            </intent-filter>
109            <intent-filter>
109-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
110                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
110-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
110-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
111            </intent-filter>
112            <intent-filter>
112-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
113                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
113-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
113-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
114            </intent-filter>
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
116                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
116-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
116-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
117            </intent-filter>
118        </receiver>
119    </application>
120
121</manifest>
