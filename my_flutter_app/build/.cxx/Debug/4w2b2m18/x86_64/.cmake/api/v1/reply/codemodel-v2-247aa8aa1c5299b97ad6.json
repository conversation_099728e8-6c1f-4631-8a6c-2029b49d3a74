{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Desktop/test/my_flutter_app/build/.cxx/Debug/4w2b2m18/x86_64", "source": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter_tools/gradle/src/main/scripts"}, "version": {"major": 2, "minor": 3}}