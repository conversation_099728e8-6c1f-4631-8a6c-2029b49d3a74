<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.pub-cache/hosted/pub.dev/vocsy_epub_viewer-3.0.0/android/src/main/assets"><file name="highlights/highlights_data.json" path="/Users/<USER>/.pub-cache/hosted/pub.dev/vocsy_epub_viewer-3.0.0/android/src/main/assets/highlights/highlights_data.json"/><file name="3.epub" path="/Users/<USER>/.pub-cache/hosted/pub.dev/vocsy_epub_viewer-3.0.0/android/src/main/assets/3.epub"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.pub-cache/hosted/pub.dev/vocsy_epub_viewer-3.0.0/android/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/test/my_flutter_app/build/vocsy_epub_viewer/intermediates/shader_assets/debug/compileDebugShaders/out"/></dataSet></merger>