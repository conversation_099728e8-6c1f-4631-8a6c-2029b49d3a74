com.example.my_flutter_app-jetified-window-1.2.0-0 /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/res
com.example.my_flutter_app-jetified-savedstate-1.2.1-1 /Users/<USER>/.gradle/caches/8.12/transforms/274b962bfc0ee3499460b07125b40653/transformed/jetified-savedstate-1.2.1/res
com.example.my_flutter_app-lifecycle-livedata-core-2.7.0-2 /Users/<USER>/.gradle/caches/8.12/transforms/2e82a987f49d0f6484c5e02710942378/transformed/lifecycle-livedata-core-2.7.0/res
com.example.my_flutter_app-jetified-core-1.0.0-3 /Users/<USER>/.gradle/caches/8.12/transforms/40dfcd36206381321d61bc4d0a504790/transformed/jetified-core-1.0.0/res
com.example.my_flutter_app-jetified-startup-runtime-1.1.1-4 /Users/<USER>/.gradle/caches/8.12/transforms/506e321e672d66bb2b4486d2ee8beed8/transformed/jetified-startup-runtime-1.1.1/res
com.example.my_flutter_app-jetified-lifecycle-viewmodel-savedstate-2.7.0-5 /Users/<USER>/.gradle/caches/8.12/transforms/51aab5cd0fa4197e7c50f04a365e85eb/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/res
com.example.my_flutter_app-jetified-lifecycle-livedata-core-ktx-2.7.0-6 /Users/<USER>/.gradle/caches/8.12/transforms/661cfed71eb01d7f0c4bbaeae6faab4d/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/res
com.example.my_flutter_app-core-1.13.1-7 /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/res
com.example.my_flutter_app-jetified-profileinstaller-1.3.1-8 /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/res
com.example.my_flutter_app-jetified-window-java-1.2.0-9 /Users/<USER>/.gradle/caches/8.12/transforms/88ea6bbee0c3d252da1a135eb4f3ca19/transformed/jetified-window-java-1.2.0/res
com.example.my_flutter_app-core-runtime-2.2.0-10 /Users/<USER>/.gradle/caches/8.12/transforms/9724ff6e8d4e6c9244bfb7005fae21c2/transformed/core-runtime-2.2.0/res
com.example.my_flutter_app-jetified-lifecycle-process-2.7.0-11 /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/res
com.example.my_flutter_app-lifecycle-viewmodel-2.7.0-12 /Users/<USER>/.gradle/caches/8.12/transforms/c7d3b67c81f17a9d01acb41ef7dc29fd/transformed/lifecycle-viewmodel-2.7.0/res
com.example.my_flutter_app-jetified-core-ktx-1.13.1-13 /Users/<USER>/.gradle/caches/8.12/transforms/d6a04692cb28023b65a80b358730356b/transformed/jetified-core-ktx-1.13.1/res
com.example.my_flutter_app-lifecycle-livedata-2.7.0-14 /Users/<USER>/.gradle/caches/8.12/transforms/dc9487afb3090e92844f4382c0195afd/transformed/lifecycle-livedata-2.7.0/res
com.example.my_flutter_app-jetified-tracing-1.2.0-15 /Users/<USER>/.gradle/caches/8.12/transforms/dcb95f8119ec5b1845af3693aa8a7063/transformed/jetified-tracing-1.2.0/res
com.example.my_flutter_app-jetified-activity-1.8.1-16 /Users/<USER>/.gradle/caches/8.12/transforms/e2fbf221633a11202a49e7e11d793bf1/transformed/jetified-activity-1.8.1/res
com.example.my_flutter_app-fragment-1.7.1-17 /Users/<USER>/.gradle/caches/8.12/transforms/fe4c05eaffed710d6c61f97a8b8ac890/transformed/fragment-1.7.1/res
com.example.my_flutter_app-jetified-annotation-experimental-1.4.0-18 /Users/<USER>/.gradle/caches/8.12/transforms/fe74d444a12af678d91d4bc25f88f447/transformed/jetified-annotation-experimental-1.4.0/res
com.example.my_flutter_app-lifecycle-runtime-2.7.0-19 /Users/<USER>/.gradle/caches/8.12/transforms/fed144bef681fc8fb80134095669b372/transformed/lifecycle-runtime-2.7.0/res
com.example.my_flutter_app-debug-20 /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/debug/res
com.example.my_flutter_app-main-21 /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/res
com.example.my_flutter_app-pngs-22 /Users/<USER>/Desktop/test/my_flutter_app/build/app/generated/res/pngs/debug
com.example.my_flutter_app-resValues-23 /Users/<USER>/Desktop/test/my_flutter_app/build/app/generated/res/resValues/debug
com.example.my_flutter_app-packageDebugResources-24 /Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/incremental/debug/packageDebugResources/merged.dir
com.example.my_flutter_app-packageDebugResources-25 /Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/incremental/debug/packageDebugResources/stripped.dir
com.example.my_flutter_app-debug-26 /Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/merged_res/debug/mergeDebugResources
com.example.my_flutter_app-debug-27 /Users/<USER>/Desktop/test/my_flutter_app/build/path_provider_android/intermediates/packaged_res/debug/packageDebugResources
