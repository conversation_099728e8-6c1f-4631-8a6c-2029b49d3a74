{"roots": ["my_flutter_app"], "packages": [{"name": "my_flutter_app", "version": "1.0.0+1", "dependencies": ["cosmos_epub", "cupertino_icons", "flutter"], "devDependencies": ["flutter_lints", "flutter_test"]}, {"name": "flutter_lints", "version": "6.0.0", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "cosmos_epub", "version": "0.0.2", "dependencies": ["epubx", "fading_edge_scrollview", "flutter", "flutter_html_reborn", "flutter_screenutil", "fluttertoast", "get_storage", "html", "http", "isar", "isar_flutter_libs", "path_provider", "screen_brightness"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "lints", "version": "6.0.0", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "fading_edge_scrollview", "version": "4.1.1", "dependencies": ["flutter"]}, {"name": "flutter_html_reborn", "version": "3.0.0", "dependencies": ["collection", "csslib", "flutter", "html", "list_counter"]}, {"name": "http", "version": "1.5.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "html", "version": "0.15.4", "dependencies": ["csslib", "source_span"]}, {"name": "flutter_screenutil", "version": "5.9.3", "dependencies": ["flutter"]}, {"name": "get_storage", "version": "2.1.1", "dependencies": ["flutter", "get", "path_provider"]}, {"name": "fluttertoast", "version": "8.2.12", "dependencies": ["flutter", "flutter_web_plugins", "web"]}, {"name": "epubx", "version": "4.0.0", "dependencies": ["archive", "collection", "image", "path", "quiver", "xml"]}, {"name": "screen_brightness", "version": "1.0.1", "dependencies": ["flutter", "screen_brightness_android", "screen_brightness_ios", "screen_brightness_macos", "screen_brightness_platform_interface", "screen_brightness_windows"]}, {"name": "isar_flutter_libs", "version": "3.1.0+1", "dependencies": ["flutter", "isar"]}, {"name": "isar", "version": "3.1.0+1", "dependencies": ["ffi", "js", "meta"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.2", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "list_counter", "version": "1.0.2", "dependencies": []}, {"name": "csslib", "version": "0.17.3", "dependencies": ["source_span"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "get", "version": "4.7.2", "dependencies": ["flutter", "web"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "image", "version": "3.3.0", "dependencies": ["archive", "meta", "xml"]}, {"name": "xml", "version": "6.6.1", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "quiver", "version": "3.2.2", "dependencies": ["matcher"]}, {"name": "archive", "version": "3.6.1", "dependencies": ["crypto", "path"]}, {"name": "screen_brightness_windows", "version": "1.0.1", "dependencies": ["flutter", "screen_brightness_platform_interface"]}, {"name": "screen_brightness_macos", "version": "1.0.1", "dependencies": ["flutter", "screen_brightness_platform_interface"]}, {"name": "screen_brightness_ios", "version": "1.0.1", "dependencies": ["flutter", "screen_brightness_platform_interface"]}, {"name": "screen_brightness_android", "version": "1.0.1", "dependencies": ["flutter", "screen_brightness_platform_interface"]}, {"name": "screen_brightness_platform_interface", "version": "1.0.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "petitparser", "version": "7.0.1", "dependencies": ["collection", "meta"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}], "configVersion": 1}