["/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/assets/test.epub", "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json"]