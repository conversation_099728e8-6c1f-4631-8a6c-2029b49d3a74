import 'package:flutter/material.dart';
import 'package:cosmos_epub/cosmos_epub.dart';

class CosmosEpubReader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CosmosEpub(
        // Load from assets
        filePath: 'assets/sample_book.epub',

        // Or load from file path
        // filePath: '/path/to/book.epub',
        config: CosmosEpubConfig(
          // Theme options: grey, purple, white, black, pink
          theme: CosmosEpubTheme.white,

          // Font customization
          fontSize: 16.0,
          fontFamily: 'Georgia',

          // Reading preferences
          enablePageFlipAnimation: true,
          showChapterName: true,

          // UI customization
          backgroundColor: Colors.white,
          textColor: Colors.black,

          // Progress saving
          saveProgress: true,
        ),

        // Callbacks
        onChapterChanged: (chapterIndex, chapterTitle) {
          print('Chapter changed: $chapterTitle');
        },

        onProgressChanged: (progress) {
          print('Reading progress: ${progress * 100}%');
        },
      ),
    );
  }
}
