# Flutter EPUB Reader

A Flutter application that can read and display EPUB files using the `epub_decoder` package.

## Features

- ✅ Load EPUB files from assets
- ✅ Display EPUB metadata (title, authors, sections count)
- ✅ Navigate through EPUB sections/chapters
- ✅ Basic HTML content rendering
- ✅ Responsive UI with Material Design 3
- ✅ Error handling and loading states

## Dependencies

- `epub_decoder: ^0.1.4` - For parsing EPUB files
- `flutter_html: ^3.0.0-beta.2` - For rendering HTML content
- `path_provider: ^2.1.1` - For file system access

## Project Structure

```
lib/
├── main.dart              # Main app entry point
└── assets/
    └── test.epub          # Sample EPUB file
```

## How to Use

1. **Launch the app** - The app will automatically try to load the `test.epub` file from assets
2. **View book info** - The home screen shows the book title, authors, and loading status
3. **Read the book** - Tap "Read EPUB" to open the book viewer
4. **Navigate** - Use the arrow buttons at the bottom to navigate between chapters
5. **Book info** - Tap the info icon in the app bar to see detailed book metadata

## Key Classes

### `EpubReaderHome`
- Main screen that loads and displays EPUB metadata
- Handles loading states and error handling
- Provides navigation to the reader screen

### `EpubViewerScreen`
- Full-screen EPUB reader interface
- Page-based navigation through sections
- HTML content rendering with flutter_html
- Bottom navigation bar with chapter controls

## EPUB Support

Currently supports:
- Basic EPUB structure parsing
- Metadata extraction (title, authors)
- Section/chapter navigation
- HTML content display (simplified)

## Limitations

- Content rendering is simplified (placeholder HTML)
- No advanced EPUB features (media overlays, complex layouts)
- No reading position persistence
- No text search or bookmarks

## Future Enhancements

- [ ] Full XHTML content extraction and rendering
- [ ] Reading position persistence
- [ ] Text search functionality
- [ ] Bookmarks and highlights
- [ ] Font size and theme customization
- [ ] Table of contents navigation
- [ ] Media overlay support

## Running the App

```bash
# Install dependencies
flutter pub get

# Run on Android/iOS
flutter run

# Run on web
flutter run -d chrome
```

## Adding Your Own EPUB Files

1. Place your EPUB file in the `assets/` directory
2. Update `pubspec.yaml` to include the new asset:
   ```yaml
   flutter:
     assets:
       - assets/your-book.epub
   ```
3. Update the asset path in `main.dart`:
   ```dart
   final epubFile = await rootBundle.load('assets/your-book.epub');
   ```

## Troubleshooting

### Common Issues

1. **EPUB not loading**: Ensure the file is properly added to assets and pubspec.yaml
2. **Build errors**: Run `flutter clean && flutter pub get`
3. **Content not displaying**: Check that the EPUB file is valid and not corrupted

### Android-specific

The app includes necessary Android configurations for file access and network security.

## License

This project is open source and available under the MIT License.
