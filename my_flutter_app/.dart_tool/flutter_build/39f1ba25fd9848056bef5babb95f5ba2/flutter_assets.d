 /Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/assets/test.epub /Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf /Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf /Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag /Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json /Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin /Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json /Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z /Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json:  /Users/<USER>/Desktop/test/my_flutter_app/pubspec.yaml /Users/<USER>/Desktop/test/my_flutter_app/assets/test.epub /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf /Users/<USER>/fvm/versions/3.32.0/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf /Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/shaders/ink_sparkle.frag /Users/<USER>/Desktop/test/my_flutter_app/.dart_tool/flutter_build/39f1ba25fd9848056bef5babb95f5ba2/native_assets.json /Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/list_counter-1.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/universal_file-1.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/LICENSE /Users/<USER>/fvm/versions/3.32.0/bin/cache/pkg/sky_engine/LICENSE /Users/<USER>/fvm/versions/3.32.0/packages/flutter/LICENSE