{"version": 2, "files": [{"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/iterables/enumerate.dart", "hash": "2bae2e1f40c4c2ad5bc1f72f07a95605"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/psd/effect/psd_inner_shadow_effect.dart", "hash": "d428598d2741da83b008efc24d567204"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/fonts/arial_24.dart", "hash": "1d0242fdfc5f36851c288a3ba2a4fcec"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/shortcuts.dart", "hash": "721fe68e34a4747334faa11e91f93523"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/iterables/count.dart", "hash": "9384fe2b5f62301923733c4d826cecfb"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/localizations.dart", "hash": "a64e270c19c9e9ed0c5d9a17e0c4a5d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/collection/treeset.dart", "hash": "cd9e31cb0048ce3c0288026cd840feec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location.dart", "hash": "fb2c02d4f540edce4651227e18a35d19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/draw/draw_pixel.dart", "hash": "3e640bf9826b2d83123ec9e1a688e434"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "hash": "edbd68eb36df4f06299204439c771edd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/lib/src/epub_controller.dart", "hash": "9c7fdda7bdf1ad9906ae1b8494ed5e4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span.dart", "hash": "b7c2cc8260bb9ff9a961390b92e93294"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/sliver_floating_header.dart", "hash": "5ffb77551727a0b5c646196e7bf1e9bc"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/bin/cache/engine.stamp", "hash": "e5616e37d36bd4c1b73d4a284c5fd7b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/psd/psd_image_resource.dart", "hash": "d8a9ef3d1223543b7b87fb672da94c62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/tokenizer.dart", "hash": "80b8464423b79136f9fc5a427a1dafb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/xml_events.dart", "hash": "81c2aad8ddfe7e91e913fa4c319764f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/processing/margins.dart", "hash": "9e044ca68c2452bec5d7302c56db2a07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/draw/draw_char.dart", "hash": "5c4987bc3d4c72a7acec3dd18d0dfbc3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/misc/failure.dart", "hash": "30a4963c49e7dd57d8cec29b8f4821db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/style.dart", "hash": "567cf361368abdfd2a4f6241d7df3bdc"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/expansion_tile.dart", "hash": "d9511b6618e15c2df1d5d0ad39256ed1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/drag_details.dart", "hash": "f350db07fdddbcfd71c7972bf3d13488"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/dropdown_menu.dart", "hash": "d110c5e3ee26058a3e9b4bba6440f15f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/converter.dart", "hash": "ed5548873fcf5a0a5614fc52139600b8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/memory_allocations.dart", "hash": "c7c757e0bcbf3ae68b5c4a97007ec0b9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/rotated_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/psd/psd_layer.dart", "hash": "549bccb0560dc75793cc27d1648ca192"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/no_splash.dart", "hash": "9c053b0efcabd70996cc27e9d6c9303e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/ffi.dart", "hash": "ae66b0cbdfe2e2a5a99c5dfa48fd5399"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/collection/delegates/list.dart", "hash": "85c99d632deaa8f2d7a3a0006db00a5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/nodes/node.dart", "hash": "9ec244272cb6c8da46a6dd5f104f0dfe"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/predictive_back_page_transitions_builder.dart", "hash": "cb745b78bdb964c02c1c4a843b9c1e7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/draw/draw_line.dart", "hash": "19f1bca1fc870167b7ad89f18b3d4532"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/definition/internal/reference.dart", "hash": "f25bbc73708cc35ac55836cbea772849"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/any.dart", "hash": "3a1d79b051bd693ad652b0f905ff1588"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/bzip2_decoder.dart", "hash": "9ecbe771e2dd8678e48ad5b690b4aaed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zip/zip_directory.dart", "hash": "457372a62869445ee1cff9a92d5746a1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/_web_image_io.dart", "hash": "e88b0574946e5926fde7dd4de1ef3b0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/range.dart", "hash": "8319b5c0133f9badc667b37194fa492d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/allocation.dart", "hash": "9d62f4f58e8d63a8e106a1158eb13a02"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/floating_action_button_theme.dart", "hash": "08c3fd9ed1607d3a707ffe9b3532218a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "hash": "deedcf7ee9b4e76191202e61654f9dcb"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "hash": "9a31689295b300aa8ab12d29fb8853ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/combinator/settable.dart", "hash": "442a233329c158bcfbb129ccea0fe8ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/exr/exr_image.dart", "hash": "189b916f5b5788de1833946cde515bf6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/webp/vp8_bit_reader.dart", "hash": "756de562a58b68c32fc927b95a94e6a8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/raw_menu_anchor.dart", "hash": "a749880c7b2c93609c79f05151beda3b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/polyfill.dart", "hash": "bc0eb13caa9c0425831f18962dfe12ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/iterable.dart", "hash": "f0db904cb4051a93b08f326f9f4ded00"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/score/score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/material_state.dart", "hash": "245a31a30063b63cbfd631fdc2ddf0d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/pattern.dart", "hash": "984f27f723ba52ab371439e37b31ca91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/utils/event_attribute.dart", "hash": "304fc982848b57cf13da0ec511f05ed9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/scroll_activity.dart", "hash": "bce1bb799fa4cc899b6525721e14c9aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/psd/layer_data/psd_layer_section_divider.dart", "hash": "63082091bc4b3748232123f88a5f73c6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/notification_listener.dart", "hash": "d3b949a1e7578291493af5fd28846314"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/star_border.dart", "hash": "e324dd19cc02a1bf47bf7cc545dcca79"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/bitmap_font.dart", "hash": "451530e9be4cb2bc8c1aba9785c8bda0"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/back_button.dart", "hash": "035b8d3642fa73c21eafbee7851cc85d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/webp/vp8_filter.dart", "hash": "af2e82a224ecfc3603f3681296ba2055"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "hash": "2baf11d03f1f50ccef5294c1fe810e25"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/visitors/visitor.dart", "hash": "87e0c94a0dd945f819a8bd24a9ac5e67"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/scrollbar.dart", "hash": "85cf42bafb7c0646bd7a99379649da29"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/flexible_space_bar.dart", "hash": "9d6f9dd391f828bccdbb47c5072c04c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/predicate/pattern.dart", "hash": "d881c458d06573eb887bdf0f3ce9f586"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/navigation/epub_metadata.dart", "hash": "59da118f1465bf1091cd3333ea9c1bea"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/physics/simulation.dart", "hash": "0fbec63144acf1cb9e5d3a3d462e244b"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/assets/test.epub", "hash": "36bad94d7dd2b9780082cf60f0924cbc"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/segmented_button.dart", "hash": "ad631d7cd122efc4862c1c084fbde716"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_buffer.dart", "hash": "f64837679a1abb526e942b166db5c244"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/xz_encoder.dart", "hash": "844b276aac80044a3bf3ff7391ea7614"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/transform/bake_orientation.dart", "hash": "fe701fd2afc5d9abb3b29768115c1ac1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "hash": "c761b80666ae3a0a349cef1131f4413d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/utils/character_data_parser.dart", "hash": "7b4a3d153a0c2e22401073c511515325"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/ink_highlight.dart", "hash": "a9e3af96f170745db1c281777cb6bda9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/nodes/comment.dart", "hash": "87546066dfc566126ed9357805535e97"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/tab_controller.dart", "hash": "40587a28640d3c90ad2e52fdfbcd7520"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/material.dart", "hash": "76611c76bf37be8fc89798858b6c7685"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/ref_entities/epub_content_file_ref.dart", "hash": "b427c600029e9e45ba1b53e525f9dd6a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "hash": "62f6d0411965eefd191db935e6594f90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/combinator/and.dart", "hash": "1e9ed9cdf00b9449d9b72dcd00add4d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/gif/gif_color_map.dart", "hash": "314f8cbc2bbfdf875fa42230b5b65a04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/entities/epub_text_content_file.dart", "hash": "c8b9abcafbf007c2d93a027c389c2da0"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/popup_menu.dart", "hash": "67d5620f72c33680625822432b60b613"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/fractional_offset.dart", "hash": "e7b2de136a99cf5253477d4fb4138394"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/extension/helpers/matcher_extension.dart", "hash": "53d29a2a6aa54d2e74e795df0dbe165a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/navigation/epub_navigation_page_target.dart", "hash": "dcfbc43f082b4b50c466d2c17247ee0b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/fade_in_image.dart", "hash": "b692d4a68a086507a66243761c3d21a6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/picker.dart", "hash": "4d8781c671b7df5aadf2331931458cfb"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/binding.dart", "hash": "a594e2e46c047f44912e93f2e38f4a47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/crc64.dart", "hash": "338a8eab73f4990e8d6aaa47f9547f2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/combinator/sequence.dart", "hash": "061e3925eb77146a83903821d09bbd58"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/elevation_overlay.dart", "hash": "ea5bbc17f187d311ef6dcfa764927c9d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/scroll_notification.dart", "hash": "269af8ca7030ccfd9c868fe9af8a6b0a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/widgets.dart", "hash": "946e37d543d3912bef54a551fb02ea1d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/drag_boundary.dart", "hash": "1e0ea989110b1544dbaf1fdf3d9864cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/monodrag.dart", "hash": "8807672a31b470f53c5fcc2b36dcf509"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/separable_kernel.dart", "hash": "136da4d2becbecc3cba5627bf5a1e39f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/button.dart", "hash": "78f88eba40852ba0b7700d94f3ecfec6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/stack_frame.dart", "hash": "eb89408ce23b2abcd324ea5afb05a1ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/events/start_element.dart", "hash": "2c72add0b4beec6c29322827553e616d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/psd/effect/psd_effect.dart", "hash": "32dc6c16dd28cf0df649467abef5695f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/router.dart", "hash": "a89f6417642d57961ee87743be4a6a2b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/ticker_provider.dart", "hash": "10cf10518abe4a916f2cb9ed7c4b635f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib/zlib_decoder_stub.dart", "hash": "d703348c150f2f9529f252667d77ee40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/activity_indicator.dart", "hash": "0e3d746a279b7f41114247b80c34e841"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/snapshot_widget.dart", "hash": "075310a7fe661b71e9a583aab7ed4869"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/multidrag.dart", "hash": "f56109c40e6fe9e53f9c6ad021d25ff5"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/hit_test.dart", "hash": "2d3948bf5dd7b63d100270fce62fa2d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib/_zlib_decoder_io.dart", "hash": "1a51de29eecde3f77e18012772a2991a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/text_selection_toolbar.dart", "hash": "2553e163ea84c7207282c18b5d9e14c1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/inherited_model.dart", "hash": "940daf4491e3ab2e15d7eac5d6ce6b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/lib/scrollable_positioned_list.dart", "hash": "8f65eab45d194326fdfabe87e44bd445"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/abstract_file_handle.dart", "hash": "47c413b312340fd698a70078e845f701"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/arena.dart", "hash": "5486e2ea9b0b005e5d5295e6c41ad3c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/streams/each_event.dart", "hash": "5776e262e9291819ba2122854943ea6d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/lib/src/wrapping.dart", "hash": "14fdc125b8694c5a67db9389f4a40228"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/png_encoder.dart", "hash": "d37c73fc07acd621a76bba7ad1bf623c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "hash": "f6d18a38c0986111a3d297424ed6fbcb"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "hash": "8af2b6b4632d42b4e1701ecda1a3448a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/LICENSE", "hash": "916a63300529f688c0eb056804e9a6de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/predicate.dart", "hash": "bd343bbe0baca1494e15a8872fe23e6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/exr/exr_piz_compressor.dart", "hash": "b35e977886e2649e4d9316fc66a0ef8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/vignette.dart", "hash": "02960f328e7b11d5ba414cf498cd9ac7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/combinator/generated/sequence_8.dart", "hash": "5f0138a157edf46a36bd960b7eaa9885"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/tar/tar_file.dart", "hash": "4fb326b6cafb49f8489cb571ac74c923"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/predicate/uppercase.dart", "hash": "c9d12a17c125e31a94ec65076a9c3ac5"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/image_provider.dart", "hash": "25b96e83b1368abc11d4aeae19e9f398"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/debug.dart", "hash": "fab9f5f0fb3bdd9295e12a17fef271c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/win32_wrappers.dart", "hash": "af7270fd3861278053b1c45a7b66ece3"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/binding.dart", "hash": "e40877daa15509fcbd3e465d246dbc09"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/text_selection.dart", "hash": "138038335aa2c209f231b2694d5aae3f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/scroll_position.dart", "hash": "94c0c017ccb267b7cacc7c047ee5b9c3"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/text_editing_delta.dart", "hash": "270de9c98f9c1284da0a6af9176ee1f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/hdr/reinhard_tone_map.dart", "hash": "f3bf7c06e667d985e3aaba1e84e0e198"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/constants.dart", "hash": "2c6facdb1b63e687304c4b2852f6ef4c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/data_table_theme.dart", "hash": "956c84257f1efe6f10ab24f3d6702307"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/scroll_controller.dart", "hash": "ec48414c6983150c30241ba7128634fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/psd/effect/psd_outer_glow_effect.dart", "hash": "e83d7b5424a3d75c6729a961b5c723e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/definition/grammar.dart", "hash": "9467e21c572f79ad7a41afb250e26905"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/list_counter-1.0.2/lib/list_counter.dart", "hash": "1200a841fbb1499e051450b6a87e46f1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "hash": "91bf94aea1db708a8378fa41de066d33"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/sliver_padding.dart", "hash": "ddf1bde8f4b9706d5769690b7819e5d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/enums/attribute_type.dart", "hash": "a9d570114e5a6e733fb029f6b3cffad7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/predicate/character.dart", "hash": "091e29d23c58b7a4b5529953044bd344"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "hash": "44d59e37041b6305018f70012fef7d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest_sink.dart", "hash": "038a6fc8c86b9aab7ef668688a077234"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/mixins/has_writer.dart", "hash": "c932575d5afb22daa2456a44889b3cdb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/png/png_frame.dart", "hash": "5a421fdf2c2c8c144de3d96b3f150323"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/physics.dart", "hash": "6e29d5e69c5745a45214fe14da377c1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/builtins/styled_element_builtin.dart", "hash": "c8966ec11d0366ee960ecdba6bbdadc0"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/icon_button_theme.dart", "hash": "ac317f8ed3b04bec644817e6f60a28d7"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/edge_insets.dart", "hash": "4349dd08c33e677b65d9e00f13c35d2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/iterables/min_max.dart", "hash": "4654dcffef3a84dc1e6df9f9e460c35e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/transform/trim.dart", "hash": "220741e48d077eaa83e04d6bbd100a8f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/carousel.dart", "hash": "7270419a025fdbf7840e542397db0c5b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/physics/friction_simulation.dart", "hash": "732535ba697d95c80d1215c0879477f1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/page_storage.dart", "hash": "e5a3ca065f292c0f0b0cca0a55df41aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/path.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/utils.dart", "hash": "05778db9e882b22da2f13083c9f28e0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/image.dart", "hash": "caad40ad1936874ea93473b300bb909c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/utils/name.dart", "hash": "2426dbde1829bfb9d5707ea69f21b4fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/ascii_glyph_set.dart", "hash": "7050c8c94b55eb51260ca54708b460fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/hdr/hdr_slice.dart", "hash": "cfc7762da78b1e309f8504242afd3947"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/choice_chip.dart", "hash": "3cd5a71cfa881a4d3d6325d6b2c6d902"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/gesture_settings.dart", "hash": "b5bd9d15c10929b4a63ea0df649e2d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/collection/bimap.dart", "hash": "acd8ae9144ccceb3651c4daaf854abfd"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/animation/animation_style.dart", "hash": "6cf1ca324535366e2ea214049ffc9918"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/tab_indicator.dart", "hash": "ecc072620f2a72e685360292690c8a68"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "hash": "8dedd49e916a59b6940a666481d82e10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/combinator/generated/sequence_6.dart", "hash": "3c158ce6f79d219073cbe23a7fe48595"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/async.dart", "hash": "3f9362642d37e0d97860181e8a1dd598"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/builder.dart", "hash": "9e5f67e1d8edbcd97531a8377e706d71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/psd/layer_data/psd_layer_additional_data.dart", "hash": "bf1dee711588963d426381de791ebf67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/characters.dart", "hash": "fa2a57b3b873fb7db4b8b961735e4ca3"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/search_view_theme.dart", "hash": "4d673eddc0bd2289539b66a92faae868"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/tiff/tiff_entry.dart", "hash": "652e4e795e6b8611185a3f9863f3e54c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/paint_utilities.dart", "hash": "853b1406f2756bef671f6d57135606f9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/form.dart", "hash": "8678afc1455a658ddf2382ad887eec66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/lib/src/scroll_view.dart", "hash": "3bac7785f0ce479219beba35d82405be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/functions.dart", "hash": "41f7bdb7d1eb3c86c21489902221b859"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "hash": "d8060c05b658b8065bc0bfdff6e4f229"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/exceptions/exception.dart", "hash": "773da8c184ab316ec6998980a1448a1c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "hash": "eca4f0ff81b2d3a801b6c61d80bc211c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/digit.dart", "hash": "e475fe11812000841b73324ccc3b3183"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/extensions/comparison.dart", "hash": "643ca26571c2ba94477233dbb914b1ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/predicate/constant.dart", "hash": "54356788d5c11fa49cae271d737b0c78"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/lookup_boundary.dart", "hash": "37f181e3096dc69dc408bf7d07fcd39a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/path_provider_android.dart", "hash": "eb368258f0f9fe56110bdc238488af97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/testing/fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/tooltip_theme.dart", "hash": "8fac1e5cad9ef06d9e55e6559c06b990"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/semantics/debug.dart", "hash": "3fd33becc9141d8a690c4205c72c5d40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/codec/node_codec.dart", "hash": "a40d7d4a17e700bbb41bf31de37c6bae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/util/quantizer.dart", "hash": "bbc7a6f0769b2d6f0a84f8f7eea86588"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/entities/epub_content_type.dart", "hash": "68e002a332a3f7b5c2f2f590e641c03c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/decoration.dart", "hash": "ae85856265742b6237ed0cb67c4364af"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/menu_style.dart", "hash": "e79db1a382e61436ed81f9f47dc06d7a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "hash": "247fd4320e1e277acc190092bf6d35ae"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/_html_element_view_io.dart", "hash": "61d3c1705094ee0ea6c465e47b457198"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/lib/src/scroll_offset_notifier.dart", "hash": "9b1f7f7181650da30de04e7895e1f7e0"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "hash": "0c520a6b1ab38e0f294c3ddbc2ec9737"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/platform_view.dart", "hash": "a8513860b3b4c160b57ca6264bc0acf8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/inherited_theme.dart", "hash": "7ebcf3ce26dea573af17627d822e9759"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/predicate/not.dart", "hash": "a7c2e189af8ef0e494c5f50550ce8500"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/stadium_border.dart", "hash": "85814d14dae3bc1d159edd0a4bef48e4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/selectable_region.dart", "hash": "81036c1ed2827ac1db9fee5a900f568d"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/scroll_context.dart", "hash": "98f725d06ba20a1032cb8770d00d7fca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/node.dart", "hash": "a5d0509a39803ffb48cae2803cd4f4bd"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/disposable_build_context.dart", "hash": "edd2f9cabffc7ea6a5a9497a1b1beccd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/navigation/epub_navigation_list.dart", "hash": "f7aed0d662e7e56dbc9652c1d5782c75"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "hash": "7bd8137185bc07516a1869d2065efe0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/jpeg_decoder.dart", "hash": "69808635ffd74954440a061ded3d2d14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/extensions/descendants.dart", "hash": "ffaf08c52f141dda6e8be50b3e46ea50"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "hash": "5061e0737e2db44e82d8a8c12f328a48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/util/neural_quantizer.dart", "hash": "febc60ea567a12e4a53e00024ae9bc01"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/_isolates_io.dart", "hash": "f90beedee11a434d706e3152bfb2fd15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/reflection/iterable.dart", "hash": "bea1f59f6923a9f56c6d7b785887caab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/readers/chapter_reader.dart", "hash": "7a979914285fd20e26bb1a2450bc1f4e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/status_transitions.dart", "hash": "59b6b74779849bf5b836b84bb362b99b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/navigation/epub_navigation_label.dart", "hash": "1bcf2011a7954d6b8572b9e90e4f0978"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/utils/name_matcher.dart", "hash": "5c4dc37f36fc78823f785b92b944560d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "hash": "05d4aeae6031730c6aa412a128f67448"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/message_codec.dart", "hash": "bf50f61746b9744a0e2d45a88815288f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/toggleable.dart", "hash": "33ce088a133276cbfd4a33ec49bdcb62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/nodes/text.dart", "hash": "d3de5e8090ec30687a667fdb5e01f923"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/events/cdata.dart", "hash": "a1bc06d1d53e9b47b32fbdb4d323f44d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/transform/copy_crop.dart", "hash": "c27ef68499548c8cea761be082f2c031"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/viewport.dart", "hash": "68eb8647107febe1419211e153b27a54"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/default_selection_style.dart", "hash": "bbc9542eb5e3c4701c24bc1268b8165c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/interactive_viewer.dart", "hash": "bb7bcb463df2ae0f5f952d439fdb384e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/bzip2_encoder.dart", "hash": "9b73a39d1b73cae05229c5b145924fa8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/key.dart", "hash": "3ee6304161ca2993b303a8074557fe66"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/material_button.dart", "hash": "c165bb259eb18a2dc493a0e7a1d1ebd9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/animation/animation_controller.dart", "hash": "01aec7b419ee4a50145b3ccdd2a85fa0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/exr/exr_part.dart", "hash": "b124ea43d0c7b65bfea3a5a71649ec78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/navigation/epub_navigation_target.dart", "hash": "684eb714d70752bc1894f893fe4cbb9a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/list_tile.dart", "hash": "837da7ede58523b5aff0ccbb40da75ba"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/asset_bundle.dart", "hash": "ef24f0630061f35a282b177d372c00d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/png/png_info.dart", "hash": "b91cdb3735564410733269d4a18e8015"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "hash": "82604e7dbb83dc8f66f5ec9d0962378b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/utils/prefix_name.dart", "hash": "fbb3e43ae57262b3fc190cb173a7b5bf"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "hash": "266a40131c9f05494e82934fd7096ed0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/action/trim.dart", "hash": "37b4a8b2d509ad6dd3f486053edecb3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/smooth.dart", "hash": "877c8707705f01a284bf577c22420d31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/utils/simple_name.dart", "hash": "208d1ef7a6cc2445551b3138139613bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/visitors/normalizer.dart", "hash": "bd502c5f75cc8148d708eb3e01c02765"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/theme.dart", "hash": "d5363426c1acae1c7410b4096cefd94d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/diagnostics.dart", "hash": "5d7b0ee48c302285b90443514166c2d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/nodes/document.dart", "hash": "8fd257a17e57f8c7a9e9c3c5d77df78b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/list_counter-1.0.2/lib/src/counter_style_register.dart", "hash": "d2c596f6a53ea0b570cf04f5799d99ca"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/switch_list_tile.dart", "hash": "d942bc7ece253c7918e1f60d35e233b0"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/gesture_detector.dart", "hash": "2354ff7691e352dd0fe56e0a46338db9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/definition/resolve.dart", "hash": "cbb8e1af9f1f0decfb6fc25a0725c51f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/action_buttons.dart", "hash": "aed826e965e4aa2fdb3466d39e33d824"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/refresh.dart", "hash": "7d5bd66d61c58afe63c6d33ee0e421c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/fonts/arial_14.dart", "hash": "d91588686b93db4d09b7c5a941f1c2a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/extension/helpers/tag_wrap_extension.dart", "hash": "599cac0a03a19b9bd2e2c7d389b50494"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/wrap.dart", "hash": "b656f459fa4dd04f817455858d3dd20f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/transform/copy_rectify.dart", "hash": "73ffda9195775dfa690cac00a6259a0e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/focus_manager.dart", "hash": "84589f907e3e4d8fc72e5c786a0530f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/image.dart", "hash": "82ffea249e4c57fb1163c3877de5c1cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/lib/src/ui/table_of_contents.dart", "hash": "405b1681d2d83381a367ffcbf9213b5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/bmp_encoder.dart", "hash": "e47f1aa6f5a1258338aa8a1e2e363417"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/switch.dart", "hash": "329bc189be2701d02fb1b7975ecf329e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/grayscale.dart", "hash": "d6f6151a7e52702d169c6aaabbe6d67f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/collection/lru_map.dart", "hash": "b4313ade510539c2447fe906b0fae4ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/util/clip_line.dart", "hash": "eba3984f4c5467d999a49c526fcdf7be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/lzma/range_decoder.dart", "hash": "72396af1725b7733f0558db5fa09e283"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/extensions/mutator.dart", "hash": "e105e8d3303975f4db202ed32d9aa4c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/lib/src/helpers/epub_document.dart", "hash": "5e59ba95eebd214aa2f72f7e40ecbc8c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "hash": "68c724edcc385ae2764308632abb76b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/pvrtc/pvrtc_bit_utility.dart", "hash": "03d68b03dbacee684ce837adc3fe6b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/webp/vp8l.dart", "hash": "54bd5b92f0fc075be64f6545ed24d57a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/css_box_widget.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/arena.dart", "hash": "04f3f5a6ad35c823aef3b3033dc66c3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/universal_file-1.0.0/lib/src/io_impl_vm.dart", "hash": "0dbaaa3c042db165a6c2698e1294ce36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/exceptions/parser_exception.dart", "hash": "a62996936bad6c27697a35bed070547d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/physics/spring_simulation.dart", "hash": "2458910beb2b4f3b177a7db027cf7d34"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/heroes.dart", "hash": "a7ca596d88ce54ac52360d6988d7c9c8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/mergeable_material.dart", "hash": "4201a655a36b0362d1b9f946b10b5e5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/navigation/epub_navigation_map.dart", "hash": "198b8a968b3924e0c81c709f744645b8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/custom_paint.dart", "hash": "43ba6279385eca1e9d14a3e4d020a3ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/psd/psd_layer_data.dart", "hash": "39245af6d97409bbd954e831bb24f01e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/trie.dart", "hash": "f67497a47a5f8508d53dea861aa1e7ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/exceptions/parent_exception.dart", "hash": "2ede71f09a240decbc57417850f8feb7"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/action_icons_theme.dart", "hash": "50dfb9886f462e2b3405f0f8d23f179b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/expression/group.dart", "hash": "e3471fd3bfb2f9217d1cf61b1bbcb43e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/animation/listener_helpers.dart", "hash": "72bbc3da5da130fb11bb5fc65614653c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/input_stream.dart", "hash": "98fb8718913406fcd5a6d334e134c448"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/.dart_tool/flutter_build/39f1ba25fd9848056bef5babb95f5ba2/dart_build_result.json", "hash": "1f8e8e6dbc6166b50ef81df96e58f812"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/nodes/processing.dart", "hash": "0ca8410c364e97f0bd676f3c7c3c9e32"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/drag.dart", "hash": "43ba7557388f413902313df64e072389"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/annotated_region.dart", "hash": "3bc33c65fa44a57d13430fdedef82bc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/draw/draw_rect.dart", "hash": "5040bfebf3e38bcd9702af7062971afb"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/.dart_tool/flutter_build/39f1ba25fd9848056bef5babb95f5ba2/native_assets.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/container.dart", "hash": "f663757bacdc28f2692b30a293d75146"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/scroll_metrics.dart", "hash": "6f18c18a1a5649f27b6e0c29dfba4dc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable.dart", "hash": "52138432903419f8457bcad45e5e6e99"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/box_border.dart", "hash": "********************************"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/texture.dart", "hash": "cd6b036d4e6b746161846a50d182c0b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/entities/epub_content.dart", "hash": "3232342c50f5a4787b8becf661e19ca7"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/menu_bar_theme.dart", "hash": "e4a748e0ab7265def948ce2f5dbce86e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/flow.dart", "hash": "34ebb85f7f2122d2e1265626cf252781"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/keyboard_listener.dart", "hash": "bd3f0349089d88d3cd79ffed23e9163b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/ink_sparkle.dart", "hash": "204fb623e2b782051e9bcb6e320e97c0"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/search_bar_theme.dart", "hash": "055a5c4a10cb9bc9f1e77c2c00e4ef9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/mixins/has_name.dart", "hash": "749e18efee29d6925d7c55e573d3eb2f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/icon_theme_data.dart", "hash": "ae1f6fe977a287d316ee841eadf00c2b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/keyboard_key.g.dart", "hash": "4f9995e04ebf5827d1352afca6adda26"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/badge.dart", "hash": "cd7cadd0efa83f26d401a14e53964fd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/processing/lists.dart", "hash": "82a5aa71a22641d7bdc4dc26579d0df5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/image_exception.dart", "hash": "12af51700f3d0ae74af9f082f22a61cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/opf/epub_spine.dart", "hash": "56cd76b4753ae62efccd4431ee07b935"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/xdg_directories.dart", "hash": "737107f1a98a5ff745dd4e3236c5bb7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/psd/effect/psd_drop_shadow_effect.dart", "hash": "23a1ce1bb6e470fe28cf713a127a4a6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/repeater/possessive.dart", "hash": "485043a68e11755920abd67f229ffe9d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/_network_image_io.dart", "hash": "be7392100d4028793c499a48ed55cf29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/core/utils.dart", "hash": "e43718c654277e0c4aadceeae9a16207"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "hash": "0763a220fcb5274b6c228b8b440ddb2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/list_counter-1.0.2/LICENSE", "hash": "fcf555f7f8ffdde7cc0fd3045a4248c8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "hash": "a0816d2682f6a93a6bf602f6be7cebe1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/assertions.dart", "hash": "d77516b410bc8410c6128cb39240acdb"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "hash": "93d025adfc0409629c51036cb0fdc085"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/matcher/matches/matches_iterable.dart", "hash": "037df9e7342fc8b812d985c8b6e8a0c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/encoder.dart", "hash": "690d691d4dfa3d5bb1f3b3bdee117d97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/equatable.dart", "hash": "1a5f064d497f9539e8e2cb4ba15a8f05"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/transitions.dart", "hash": "22ad3e3602e0fc7a63682e56a5aeaac0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/page_view.dart", "hash": "7150d31ecb453ea0d7516ebd2a56ff84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/LICENSE", "hash": "7ed57e479e6c5b4b7533c3996bd0d83d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/style/display.dart", "hash": "d3e13a13141ab46d6f2bbcfef6676457"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/sobel.dart", "hash": "feb407a2afafa3df74741881abd48c20"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/bmp/bmp_info.dart", "hash": "b9f058fc3bc72cf74ad77eb0d60c7ca7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/converters/node_decoder.dart", "hash": "16eac0a8fcc5fdae0d8f38b7ea301c37"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/lowercase.dart", "hash": "05b3f9197904fe6acb3facfa980e097e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/gzip_encoder.dart", "hash": "0ab8a127ada2857bf6593f8daa6ea174"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/animation/animations.dart", "hash": "57d74766f36a3d72789bc7466ae44dba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/constants.dart", "hash": "0672d853d5097a03eddc7dbe558eeabd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/exr/exr_b44_compressor.dart", "hash": "acb007ee7ff20d1161733f85a6bc9f44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/matcher/pattern/pattern_iterable.dart", "hash": "f0ae0acd94eb48615e14f6c4d1f5b8e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "hash": "019f7b771f1865632d5a36c9e74296db"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/_timeline_io.dart", "hash": "90f70ffdd26c85d735fbedd47d5ad80b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/core.dart", "hash": "adc6024b49dc43b980a62c9ff028f8c2"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "hash": "ff2b2e7159e19374f968cf529da25c01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/util/dither_pixels.dart", "hash": "f8fdf078a5697b65796b8ea07c1a79eb"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/restoration.dart", "hash": "79d4fba74eb854577c9589fb33994287"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/two_dimensional_scroll_view.dart", "hash": "28e91fd9077820e2cb2eb981471636ca"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/magnifier.dart", "hash": "4da5ad5941f2d5b6b3fbb3f7ea217b41"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location_mixin.dart", "hash": "6326660aedecbaed7a342070ba74de13"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/button_style.dart", "hash": "982099e580d09c961e693c63803f768d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/colors.dart", "hash": "58490e33e6e99c4e4e313491a36cf23f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/magnifier.dart", "hash": "52d0e96cbfe8e9c66aa40999df84bfa9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "hash": "ef5fc00d685cd2a36c4de80e1c7e3a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/_file_content.dart", "hash": "10a182ab0a81c3e666f25f65d8f84766"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/LICENSE", "hash": "9633ac2bb6bd16fe5066b9905b6f0d1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "hash": "146741f6f87d6612ee7bbf6a6fa9c119"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/filled_button_theme.dart", "hash": "52beedf1f39de08817236aaa2a8d28c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/exif/exif_data.dart", "hash": "834dd20ffcf81c673d8942dec7fb3c04"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "hash": "107c33a245427bf0f05e21c250653dc6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/dropdown.dart", "hash": "095edf197865d16a71124cfaa427e31f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/platform_view.dart", "hash": "72804f9d34b9a247c43d6cc575527370"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/alignment.dart", "hash": "ccdbac117e9349d3ceaa005c645277e2"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/observer_list.dart", "hash": "8ae04de7c196b60c50174800d036642f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/input_border.dart", "hash": "2aec07fe4a1cd25aa500e5e22f365800"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/basic.dart", "hash": "e5ebffb07608ee2f93a7aa4c23848564"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/filter_chip.dart", "hash": "0e13760edcb9f90f659ba77c144a3461"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/animation/tween.dart", "hash": "73f043194b9c158454e55b3cafbdb395"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/search_field.dart", "hash": "6dbd6092d46d1cfb37491463002e960e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/slider.dart", "hash": "48a02b5ec3a8c6127b28927b5960d076"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/synchronous_future.dart", "hash": "fb23ec509c4792802accd10fa7c8a6b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/transform/copy_resize.dart", "hash": "154934b42f88d287aa698004f1891f6b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/layout_helper.dart", "hash": "1fd7c932679011d491315ff136d13822"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/navigation/epub_navigation_page_target_type.dart", "hash": "669560cb4b35199145f7ed339a781fc0"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/borders.dart", "hash": "5de15d7a41897996ef485c087ef4245b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/widget_inspector.dart", "hash": "0bda807c0c8098d0ca933cde19f49516"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/text_field.dart", "hash": "29d1f8b59096b4d11d693c4102a08499"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/extension/extension_context.dart", "hash": "7b3d304be22be0251b428ab69f9fdb52"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/text_selection.dart", "hash": "9c13d1f810b039faf38c54f062c83747"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/context_menu_controller.dart", "hash": "c3ccb5b6cd3df44e6587a4f04dd6a4e7"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/placeholder_span.dart", "hash": "d2386b256656121d501a16234b008e2b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/paragraph.dart", "hash": "d9eb28b2265932eb628ad0c3a123bee7"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/assets/test.epub", "hash": "36bad94d7dd2b9780082cf60f0924cbc"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "hash": "3fa7a3bafbab98c305119475eb004a06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/md5.dart", "hash": "0981c95a357b5cebc932250a5e6c988e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/framework.dart", "hash": "f9963c0de15655f08d11298175dd45fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/lib/src/element_registry.dart", "hash": "1e27dcfcd0dcb4e697a64c772c71e587"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/lib/main.dart", "hash": "d8739ba0f9a072690b9ab0c38f80e8bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zip_encoder.dart", "hash": "50f4059a7cc84a666cd0d6d0fa5256d7"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/binding.dart", "hash": "530c4f96f1475cc4e4128ffedd705028"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/combinator/generated/sequence_7.dart", "hash": "e7f41e9640a11f484fe97a34fd0c6fe4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/raw_keyboard_web.dart", "hash": "547eac441130505674f44bf786aee606"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/lzma/lzma_decoder.dart", "hash": "8e6aa847a915de368f0e2f98303878c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/utils/labeled.dart", "hash": "715bccb8e9ba9889573a60bf0e457402"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/predicate/range.dart", "hash": "5e99407d87eef382375ad62495706f32"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "hash": "a309d8ca64c3efb3ad74b742ffb0e1dd"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/arc.dart", "hash": "511ff5c6f0e454b22943906697db172f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/process_text.dart", "hash": "94235ba74c3f3ad26e22c4b40538ce07"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/banner.dart", "hash": "c9cd996cea2334f644c74ebbdb41f7f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/char.dart", "hash": "7a6d53ccbed48dd524627ee1a945ac15"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "hash": "6e22c7f1454c97560ef83096561678dc"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/animated_icons.dart", "hash": "78ce7527fa364df47ba0e611f4531c2c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "hash": "5c96449c2a494ea8f3a50ecc3ba9af74"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/clipboard.dart", "hash": "61137458bbcab0dfb643d5d50a5ae80f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/tiff_encoder.dart", "hash": "7926131e2a0cc0ad1569dc832f421405"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/lib/src/data/epub_parser.dart", "hash": "ef8d602c0d766e9bc01d24d6b44669c1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/menu_button_theme.dart", "hash": "e461dc9f79fcf6a9e4faf12c8182fb47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/messages.g.dart", "hash": "3e127bbafbce223b6d416d5cca517df7"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/selection_area.dart", "hash": "ed28f6ca17f72062078193cc8053f1bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/predicate/predicate.dart", "hash": "c135a8cfe6154841111bd7d4f7c7e69a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/navigator_pop_handler.dart", "hash": "0d1b13fd16692571d5725f164d0964ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/utils/sequential.dart", "hash": "b5519514c9b9570c951c0da186030e29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/exif/exif_tag.dart", "hash": "15d855db7429121f757582e25149afe4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/misc/label.dart", "hash": "7de7aec8bf9b53488692403a3feb7672"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib_decoder.dart", "hash": "f579367d110cce54117aec036fdb4074"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/style/lineheight.dart", "hash": "c9b1ea9c0c89817430f0339ab66ba49a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/core.dart", "hash": "b969cd0066fa07b8082edb76d2af77e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/lib/src/data/epub_cfi/_parser.dart", "hash": "9a2d2ad50d6ada0f4a14f557de0dc4cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/proxy_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/annotations/annotator.dart", "hash": "d45b4bb922c2941476a8b797e0e275ad"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/tab_bar_theme.dart", "hash": "a91b4b0d0d10b955e8973126cf288ea4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/elevated_button.dart", "hash": "c2dcf2bcdc85d007f9729621d13cccf4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/navigation_rail.dart", "hash": "2936420e0c8ddba21d283d969f5147d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/archive_file.dart", "hash": "23b52b19a3120b5d10380eac9dbede3b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/collection/delegates/iterable.dart", "hash": "bddaf7917999104e7ff80bc691258b6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/ref_entities/epub_text_content_file_ref.dart", "hash": "06b358b1ab94b8517fd6d4f45631c0c3"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/tabs.dart", "hash": "cf13158636a678584cee9f3b0036bce6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/elevated_button_theme.dart", "hash": "484329e20b76c279413a7d6dc78b3223"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "hash": "ac08cb84358e3b08fc1edebf575d7f19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/tar_decoder.dart", "hash": "9d35e65a23a3fd6bcb0a545ee833f8c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/tree/replaced_element.dart", "hash": "d4b2f5acdeed0379a06952d670d21b77"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/physics/gravity_simulation.dart", "hash": "44c1268c1ecafd3b4cd06ab573f6779a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/core/hash.dart", "hash": "af7c8c836bfd5501fafcf51657721220"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/collection/delegates/map.dart", "hash": "686b54f3267ed21cf232cf8d05029433"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/semantics_debugger.dart", "hash": "2c5021ff8faa0330f66b1c501e8d4b22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/jpeg/jpeg_frame.dart", "hash": "6d196769966e6d39d7ce060671e7221a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id.dart", "hash": "32f5f78e5648f98d8b602c6233aa4fc5"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "hash": "6aae6e13d1c46ed5f772f06e3cea93fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/events/processing.dart", "hash": "5a7bd956aa537e95be882d4809232c39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/expression/builder.dart", "hash": "edd1157c0a6badd18824781de14280e6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/outlined_button.dart", "hash": "438f80a3d5361329aa6113e3409440aa"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/autofill.dart", "hash": "3623c605586d2e37af23d6b746721bd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/color_offset.dart", "hash": "755cd641a6e1318d63513d1977b2c04a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/decode_info.dart", "hash": "382307f3c524d87e518daa0ced050c3c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/system_context_menu.dart", "hash": "5666a74f3f21ee2fa9b0b2aa37360700"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/entities/epub_content_file.dart", "hash": "b66f1662b2a2f22bdd650ed4e34aa8e4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/input_decorator.dart", "hash": "952fb243dbdb00bfe11b0293238b115d"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/pubspec.yaml", "hash": "460734d3a89e3d9f8680866afa47ddfd"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/spacer.dart", "hash": "d2372e0fb5a584dcd1304d52e64d3f17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib/inflate_buffer.dart", "hash": "b47889f85ed5635c5010e4a3cbab97af"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/events.dart", "hash": "89aeee125822690cbd46b2ff43c76ec1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/decoder.dart", "hash": "2bd76464e54527eea5e72932e3968e14"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "hash": "8a39bdc324d0ff25097784bd98333c08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/action/permute.dart", "hash": "8171c3b0d66f560aad82b73d43393092"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/clip.dart", "hash": "dc2cfe4408f094916cd5eb1d294d1f2f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/physics/tolerance.dart", "hash": "43ef2382f5e86c859817da872279301e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "hash": "555fcdeebbe6517cde1cdd95133cabd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/psd/psd_channel.dart", "hash": "66d8ee44dd84494a1c1c892e841d4c33"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/semantics.dart", "hash": "4b784d6e4f290bd6d5a1f38bfb5701d8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "hash": "991024814d51967a20be5851be93a8e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/utils.dart", "hash": "caf148b76c44a3f0f1bd6055ddbb8f5e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/outlined_button_theme.dart", "hash": "8ece5be4aa5c8fa615288c4c8c5277a2"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/decorated_sliver.dart", "hash": "cd7f8dc942f5138db121aabbaba920ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/exr/exr_wavelet.dart", "hash": "697c8bda25da5eaa6330c962715acfe2"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/decorated_sliver.dart", "hash": "4b50828d394e7fe1a1198468175270d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/streams/with_parent.dart", "hash": "5e97dbe19781055ba2585ce570bc4643"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/utils/namespace.dart", "hash": "d7259aeee1602df30d051e8fc0523d91"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/route.dart", "hash": "7e827f3c407d93dfa01d1c8cac14af80"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/spell_check_suggestions_toolbar.dart", "hash": "12120b49ba363d4c964cf1d043a0aa1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/LICENSE", "hash": "863b2e072742234a338d6313113e5df2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/jpeg/jpeg_component.dart", "hash": "46ef101278d221e5d3180030fb830202"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/scrollable_helpers.dart", "hash": "7f2ccd6eece375fce2e247d3995e45c5"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/colors.dart", "hash": "f59aed120736d81640750c612c8cfe5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "hash": "9434ff8aa06e13d5981ed6ec15eceb64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/noise.dart", "hash": "538624d2458f6710809f670ee4da6239"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/lib/src/data/epub_cfi/_interpreter.dart", "hash": "832db6e655107f5f81bfac59fb0ef874"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/webp/webp_frame.dart", "hash": "2af1c3ac18abdc3424037ed8adfd4201"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/opf/epub_metadata_creator.dart", "hash": "5e8de57368fe8e412341158efdd53c44"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/navigation_rail_theme.dart", "hash": "e472fd233266592e97b3fb39bb1a11dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/opf/epub_metadata_date.dart", "hash": "192240c87054417e43127a603735738a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/_bitfield_io.dart", "hash": "0ae47d8943764c9c7d362c57d6227526"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/navigation/epub_navigation.dart", "hash": "01d607400ab25bd5a77d966470b973c1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/icons.dart", "hash": "790dc5e1e0b058d13efbd42a3f46498e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/hdr/half.dart", "hash": "2d3668278fec324992cafb433b7ff805"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/basic_types.dart", "hash": "2346472ec1cfdb77f3b27d3b7af72d4c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "hash": "2adcbf9fb509dd8fe8864a702db29043"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/_platform_io.dart", "hash": "bf6d84f8802d83e64fe83477c83752b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/lib/src/data/models/chapter_view_value.dart", "hash": "e19eaa44a84ceac146638f8a9380ff98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/navigation/epub_navigation_head_meta.dart", "hash": "0e466b439f9e11424ca641e9cb4875f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/jpeg/jpeg.dart", "hash": "f4d5353e3da20de001d324e261d6c877"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/icc_profile_data.dart", "hash": "2a03d0680a6ee4a60dca0a8f768769c0"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/page_transitions_theme.dart", "hash": "91f73f40856927e688e1707a923db3e2"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/scroll_configuration.dart", "hash": "c9ab6d9cf33f78fef3ff4ad99fc73390"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/error.dart", "hash": "6cae6900e82c94905cc2aaefd806f8eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/transform/flip.dart", "hash": "ba7f4b32a9ef495636b852d12e0b1591"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/entities/epub_chapter.dart", "hash": "a5531645acecb3bbb7a3463dce8dc98c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_extensions.dart", "hash": "5843b4750179f6099d443212b76f04a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/draw/draw_image.dart", "hash": "938d38a64fe323b9242ad33a69a0d6cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zip_decoder.dart", "hash": "bacc4b237d160c70f1af46b56cd45495"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/ref_entities/epub_content_ref.dart", "hash": "ad9ec72223addc732f547dec7a8e79a6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/service_extensions.dart", "hash": "d7a6c07c0b77c6d7e5f71ff3d28b86bd"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/button_bar_theme.dart", "hash": "0f717ff4ecfdaa0347894abbedd5d1e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/webp_decoder.dart", "hash": "0be044192832906830bca11259ecd908"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/timeline.dart", "hash": "2fbba4502156d66db0a739144ccce9a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/wrappers.dart", "hash": "21e56afda1f096f0425a34987708ed56"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/motion.dart", "hash": "505f6c9750f9390c9e9e4d881092cef4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/selectable_text.dart", "hash": "130ada4ea6283eb536d5d8eb0786a631"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/temperature/temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/remap_colors.dart", "hash": "738d0fec38aeaaa0138ce710b7076633"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/lib/src/data/epub_cfi/epub_cfi.dart", "hash": "68dcc52d017634e29272eb6567fc6bce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/action/continuation.dart", "hash": "95adecf7ec0db3c154665406582e0513"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/gzip_decoder.dart", "hash": "903c5366b070dfc4a51c86a87aaf599b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/iterables/zip.dart", "hash": "77ad9a54c3b4607aa66fd050ae3088b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/utils/separated_list.dart", "hash": "82acaf4c715888e486eb9d714c23b266"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/chip.dart", "hash": "728c8c2ffdc4b584c67df65b41e6461f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/adapter.dart", "hash": "e05529d31a09e4c86cde70483824fa10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/internal/bit_operators.dart", "hash": "b56b046f016d3db74b22e2d36f322a3a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/mixins/has_value.dart", "hash": "2aef91d8cd008f57a605919dba2b095b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/source_span.dart", "hash": "9f2eb24284aeaa1bacc5629ddb55b287"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/utils/resolvable.dart", "hash": "f7329cc0811af555900320e49bd9686f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/animation.dart", "hash": "29a29ed9169067da757990e05a1476ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/utils/token.dart", "hash": "007b05bbaaa5af831aed126b4db596e5"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/button_theme.dart", "hash": "7b0e6dd1794be4b575ecf8af6475f0e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/mixins/has_visitor.dart", "hash": "61e938fe770ed7331e39f1dda1b64dd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/collection/utils.dart", "hash": "cc15b7d5941d0fcab1883f84e9646da9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/radio_theme.dart", "hash": "3f2a39352a1c6067566f8119aa021772"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/matcher/pattern/parser_match.dart", "hash": "d742d41268dec3da5e669142ae344928"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/change_notifier.dart", "hash": "ce666dc6b4d730d3cb07e6bfc64a8825"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/webp/webp_filters.dart", "hash": "e63b01261cc8830071579a204aa9b8e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/collection.dart", "hash": "4ba0a4163d73b3df00db62013fb0604e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/stack.dart", "hash": "2cf5ffb71954128b5e80f17a36bcde43"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/lib/src/helpers/epub_view_builders.dart", "hash": "c3a536a60c07cf14209a821b63687bd0"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/scheduler/debug.dart", "hash": "d72a4ddaf6162d8b897954e02b4a2a4c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/navigation_bar.dart", "hash": "6feba69fa8c1010e5be3e6d5fa4c0d71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/bzip2/bz2_bit_writer.dart", "hash": "edee0987f0b4bb1e5bb4e7a9f3e5135e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/style/length.dart", "hash": "7ad2eccec42ebdc0e7dc05b83c7e05ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib/_inflate_buffer_io.dart", "hash": "de2ee2841614931c031c4596eaab3cb7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/exceptions/tag_exception.dart", "hash": "65fe1b7c956a57db85d24838ab970d2d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/text_selection.dart", "hash": "0c46b12a4e0301a199ef98521f0ed3ab"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "hash": "ffa4f7b2d5b1caccc05cf4b64021ae5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/draw/fill.dart", "hash": "114ae8c7c915c440e4311e5dbe19b6a0"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "hash": "7e0e723348daf7abfd74287e07b76dd8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/inline_span.dart", "hash": "e3127548d819af5ec9ecb10b5732b28e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/color_filter.dart", "hash": "bc3c12f9555c86aa11866996e60c0ec9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/scrollbar.dart", "hash": "8e7a6f654b6ef374af586747a3ea912b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/shared_app_data.dart", "hash": "feacc941aea1ec8b3a30601915b7d353"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/formats.dart", "hash": "387dbf21814af0ea3735583743435de8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "hash": "56a764067b45a1a7cb6b7f186f54e43a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters.dart", "hash": "99b4d15f76889687c07a41b43911cc39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/draw/draw_circle.dart", "hash": "de4e50f0d530720dafedc63780ecd145"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/effects/drop_shadow.dart", "hash": "276d75b103ef82bab1f1f8caec90c559"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/scroll_view.dart", "hash": "3d5ecec2ff4236c99de1acef7a20a152"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/foundation.dart", "hash": "b4a0affbd6f723dd36a2cc709535c192"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/tar_encoder.dart", "hash": "10693856cc2bace0b2cd3b65fa24d974"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "hash": "a06bb87266e0bac30a263d7182aaf68c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/form_row.dart", "hash": "5f64d37da991459694bce5c39f474e5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/converters/event_encoder.dart", "hash": "ff402ced5472590045b91c0f30e4b089"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/iterables/concat.dart", "hash": "afc4cedbff166c165594b4f723699591"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/readers/book_cover_reader.dart", "hash": "185bbbcd753a56940ee48a0bcbfbfcc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/predicate/letter.dart", "hash": "4165baac3466972c71160f4aa15cd185"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/binding.dart", "hash": "f6345e2a49c93090bc2e068a0a808977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/cur_encoder.dart", "hash": "feb6806516e3facf5588eb04c404790c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/algorithms.dart", "hash": "0976264b99a1702a5d74e9acb841b775"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id_real.dart", "hash": "0e5b422d23b62b43ea48da9f0ad7fd47"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/LICENSE", "hash": "1d84cf16c48e571923f837136633a265"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/undo_history.dart", "hash": "73089c9737db54a05691e09bc9fc1bcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/lib/path_provider_foundation.dart", "hash": "fb40d5cc467bafccfcf35fc7d12adb0d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/animation/curves.dart", "hash": "74a89d22aa9211b486963d7cae895aab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/predicate/single_character.dart", "hash": "8db9443001d816c1f89abdf5bc0e7c7e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/tokenizer.dart", "hash": "a8e51be045a7977648c023a4531317f2"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "hash": "f500fac00bc25f66e6f49f5ca6de723a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/selection_container.dart", "hash": "97359ca5bc2635f947e7616f792565c6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/magnifier.dart", "hash": "b56cf23d49289ed9b2579fdc74f99c98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/html_parser.dart", "hash": "941f89afe096f82889fb797a1f3834b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/gif/gif_info.dart", "hash": "057a5bda1e97107b8318e5070b1f3b5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/invert.dart", "hash": "6775960b124ef38eb2f17e3192bb6c61"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/semantics/semantics_event.dart", "hash": "c069ad8b31e18adb75c27530f218957a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/extension/helpers/image_tap_extension.dart", "hash": "857fcc4b7c6d671ad0d12b5e51cc3b53"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/app_lifecycle_listener.dart", "hash": "f77f6a903d346f842a7fe474e427d6a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/converters/event_decoder.dart", "hash": "3d7fed590b9d1ab99d591b04487b9287"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_data.dart", "hash": "b9abba31a48a9c2caee10ef52c5c1d0e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/app_bar_theme.dart", "hash": "62a38b21e9ef4b8a8d5ae1db1c355bd1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/box_shadow.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha1.dart", "hash": "dfb8ebcfda08e6d9b294f49d74ad9f98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/iterables/range.dart", "hash": "c2c4c0a3bf82b934c09dc81cf4f91983"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/opf/epub_metadata_identifier.dart", "hash": "c9caff5b237ed5ad965c48e42157f173"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/text_theme.dart", "hash": "ee36aadc3fac54d5659c94c6aadcd007"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/utils/code.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/tga_encoder.dart", "hash": "e27651e8d018be2f4bbbab3d0dd3b704"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/extensions/string.dart", "hash": "1aaa0309ba77b0f57733e99543c455ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/dtd/external_id.dart", "hash": "3598a401936c6a9e0a645251fba246f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters_impl.dart", "hash": "6297da5be01fb7c0d5c4aaffe7a27a50"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/text_selection_theme.dart", "hash": "166478d231aa67eb8e47a7b559955e6b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/jpeg/jpeg_jfif.dart", "hash": "2442a18c6497fffdcc95eabeca48fd2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/LICENSE", "hash": "85b88c0adf3b607ae86ed16b6fb4cc1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/repeater/greedy.dart", "hash": "01b051da3c61c872efd639af5fa0f4f7"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/typography.dart", "hash": "eea9d5a977d3ff4f46bb63a0f140c738"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/widget_span.dart", "hash": "84e117adf104c68b0d8d94031212b328"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/util/random.dart", "hash": "3f3f45e6eab181f16e52051eee964bc4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/lsq_solver.dart", "hash": "d0ab7f5e11e48788c09b0d28a0376d80"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/constants.dart", "hash": "83df4f6e4084a06a4f98c27a524cc505"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/action/cast_list.dart", "hash": "87751ee02d315bd2d0c615bbf2803a3d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/scrollbar_theme.dart", "hash": "b3019bcd49ebc4edd28b985af11a4292"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/grid_paper.dart", "hash": "6aad5f436704faf509d60ddb032f41b4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/debug.dart", "hash": "17fec0de01669e6234ccb93fc1d171f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/list_counter-1.0.2/lib/src/counter_style.dart", "hash": "ccaba0f05fa59f205a1b2f7abaea28f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/opf/epub_package.dart", "hash": "c000a4cb8a2c3b02a647d727e1d9f990"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/scrollbar.dart", "hash": "a2d1c7bec7b52901761f3d52a1ac02d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/action/flatten.dart", "hash": "b192f8c8e04e47ae69d662e5feff7306"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/notched_shapes.dart", "hash": "7821d01f98c559fcbec46a41b4df7ebf"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "hash": "d3b40ca9660164ac83b714d6e2df3843"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "hash": "11fc97acd20679368ae2eaa698c6f130"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/entities/default_mapping.dart", "hash": "a2187618f84ad697f470a748b2a27f56"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/ink_ripple.dart", "hash": "81fd3ef494f4443fb8565c98ba5a9ba2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/combinator/generated/sequence_2.dart", "hash": "312e69b666cc1e860274006e86688bf9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/pvrtc/pvrtc_color.dart", "hash": "2aaaa193a61c9c9932cf3b6e79348668"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/text_painter.dart", "hash": "93576d7d8731bea65013886f9194df15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/draw/fill_rect.dart", "hash": "ae702e2fd6bee04bf3639d2f3460efc1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/service_extensions.dart", "hash": "920b63c794849c8a7a0f03f23314bbb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/color.dart", "hash": "745111fbae68c04812704b86d645b644"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/navigation/epub_navigation_doc_author.dart", "hash": "6a8e46a3f23efbb22e3a4164dcc0eb6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/extension/helpers/tag_extension.dart", "hash": "d75e1710ac4e21f73dde65c4b01fe58c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/entities/entity_mapping.dart", "hash": "5abb58e10e8ea85ea5990a97ee20ae4e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/encoding_parser.dart", "hash": "109eeb63e43422d207e9ad771c2ab623"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/streams/flatten.dart", "hash": "481d21ef07dee6f82302a015f989b597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/nodes/declaration.dart", "hash": "79198336b26da3116eb3cf2258e9f72b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/actions.dart", "hash": "1c7764fa08241a44711301c74fb658df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/exr/exr_channel.dart", "hash": "12cb4aeffc2e164279f8121ec18a1f51"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/resampler.dart", "hash": "cad4582fa75bf25d887c787f8bb92d04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/opf/epub_guide_reference.dart", "hash": "24ba67621b50501bd52ddefc4d96e1aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/text.dart", "hash": "7217dd37b49bab8e0319d4fb26d14d8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/glyph_set.dart", "hash": "62d88517fa4f29f5f3bcec07ba6e1b62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/colors.dart", "hash": "c517fb54b3d66b22988ad7c8d07c6f53"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/debug.dart", "hash": "9f05403438068337dd8f3433d2757535"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/unique_widget.dart", "hash": "11b4d96c7383b017773d65cb2843d887"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/path_provider_linux.dart", "hash": "8ac537f4af05ad812e8cd29f077aee24"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/lib/epub_view.dart", "hash": "546da4e02e11af3f7d1433e7c3b10d3a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/token.dart", "hash": "a27310d4435c84885993bedb05adabfe"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/table.dart", "hash": "9b98b196040f00fd2fbaf5f7a2309e6b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/shadows.dart", "hash": "36fc598c656490ab430ca1be5fb909e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/eager.dart", "hash": "07664903d8026f2514b29b786a27f318"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/html_input_stream.dart", "hash": "ed02ce14880085c75d4dbc4b3145371d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/token_kind.dart", "hash": "4b721bbf0c0f68e346e09e254b6b8d5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zip/zip_file_header.dart", "hash": "df75f6f36187d9129fd7f1b2041836a5"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/navigator.dart", "hash": "047052ee1e98c394dd79f1ddf5983b4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/LICENSE", "hash": "c23f3b290b75c80a3b2be36e880f5f2d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "hash": "97f7922aea45c38413930285b604bf18"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/sliver.dart", "hash": "dc037755b1140b31ffc8295fb9570cff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/separable_convolution.dart", "hash": "acbbcb4e6fd03098914cb8989664b604"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/binary_messenger.dart", "hash": "056355e344c26558a3591f2f8574e4e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/core/parser.dart", "hash": "bb70d2e76c8609b7a22250037d9185f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/tiff/tiff_image.dart", "hash": "317b3189e9d7558776f6b98c03a28d9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/serialization.dart", "hash": "f20071b459b9bbb98083efedeaf02777"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/tab_view.dart", "hash": "8b15d222f5742b46bf55a4ef4cbfd6e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/src/hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/jpeg/jpeg_data.dart", "hash": "c95f447f26bf12663a6186b3f8a02120"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/filled_button.dart", "hash": "3de98898d0fea89f0e609dcbf7b69783"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/sliver_grid.dart", "hash": "b61a261e42de1512c8a95fd52ef6540d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/extensions/preceding.dart", "hash": "9d5375413b37f738384990ebdd6c6285"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512.dart", "hash": "e4973bdb8ceac8b88cdefee5f56f0fa0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/extensions/parent.dart", "hash": "210257ed62edd783098ed34d7cfb0204"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/view.dart", "hash": "e758d8d6b65597325bd35b5dc769c7a2"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/unicode.dart", "hash": "8b525140e1bf7268e1681a62c7640eea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/collection/delegates/queue.dart", "hash": "9f25c481da0e8d72d2bf21bfc29888bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/matcher.dart", "hash": "faa18ee55924a5c65995875c94338d98"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "hash": "f8275b74f8f83272b8a8d1a79d5b2253"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/empty_unmodifiable_set.dart", "hash": "0949b8197a6069783a78f4bb0a373fb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/tokenizer_base.dart", "hash": "e3bb2a25791065817d184fabfb8f7d0c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/text_input.dart", "hash": "a4c1dffb16d559eb4d22bac89777780e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/dialog.dart", "hash": "fdf500742b45dff0abb3db9cbd350fd4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/text_style.dart", "hash": "0cf873bc441372ec89d746477273af13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta_meta.dart", "hash": "0cf5ebf6593fabf6bb7dfb9d82db735b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512_fastsinks.dart", "hash": "7924bc2d95999b2767d9f34e6ac52f98"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/animated_size.dart", "hash": "6b396237a38f3417babe500724de8a84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/parser.dart", "hash": "668feba83ac51da82a0cd90d035b271b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "hash": "dc3d03800ccca4601324923c0b1d6d57"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/basic_types.dart", "hash": "785eedcc96fa6a4fcc7c81a8736a7427"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "hash": "a46ede2164234d7371852e8f57865dd0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/css_class_set.dart", "hash": "fd47de61e362c730e345626317a8fc44"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/flutter_version.dart", "hash": "ad5b018b42f4cfaf02739e10a48c3ca3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/readers/package_reader.dart", "hash": "ab5c30d084fbc2eed8becf53e3def838"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/tooltip.dart", "hash": "c816d604c95b060fbb4fa0831ad7523d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/list_body.dart", "hash": "18223495a47aa96889552c9834042729"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/standard_component_type.dart", "hash": "09973ba0a94d2d819052c0544dcdce70"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "3405e08e614528c3c17afc561d056964"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "hash": "2936a409e1029ec52f7c0003f4db18c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/builtins/text_builtin.dart", "hash": "e8397b5ffb383b305d321c752b3666b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/shared/pragma.dart", "hash": "6fc8c606a9db58715ea15f5ee1e062fb"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/action_chip.dart", "hash": "c7d65c476f653e952aedcb0cbcab3c73"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/matcher/accept.dart", "hash": "740f17823564c3c7eca15bca5c110e17"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/live_text.dart", "hash": "7da554c3a69a1c2d019202e3f63331c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/aes.dart", "hash": "5bbe37094357ce641996c2550b14c417"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/grid_tile.dart", "hash": "9c169d41e4740bbc21d0ce33bc753119"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/bottom_sheet.dart", "hash": "c442be28b905f64b74f6e9f8e5903820"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/list_tile.dart", "hash": "8b20b418804c1d6e59afdfcae6e84728"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/predicate/digit.dart", "hash": "fc5bd8041afab0229dff18f2011a51a5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/mem_ptr.dart", "hash": "ef30f30878ef0ee91ca912d2adec344d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/tga/tga_info.dart", "hash": "960d6c06beead29fc998abb1ab481aeb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/LICENSE", "hash": "387ff7f9f31f23c3cf5b17f261a091bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/repeater/lazy.dart", "hash": "c55ebccc440e68cd5b9553b5cadb9781"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/readers/navigation_reader.dart", "hash": "71a65657ea6ef9b8e61fa2338e846d60"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/data_table.dart", "hash": "752b2b12f0829a4d0abb699adad87062"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/lib/src/positioned_list.dart", "hash": "9bb3f1fe4e6a790276ed89bbf3d22170"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/visitor.dart", "hash": "27780bbb98adce3f00386fc6223bf2c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/repeater/unbounded.dart", "hash": "a617a91b12a3156406da1d95552aa4a0"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/print.dart", "hash": "458f3bf784829a083098291a97123e81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/gif/gif_image_desc.dart", "hash": "0061b21acc302d5817a5119818483904"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/dom.dart", "hash": "6a64fecc9f1801803c9a6706f60ad958"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash.dart", "hash": "4af79c5c69ccf0cae6ab710dfb84b125"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/semantics/binding.dart", "hash": "d5bcdae8bba4c191294311428a954783"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/mouse_cursor.dart", "hash": "b0c6844b0af0cd0539060a0bfcbe3713"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/annotations.dart", "hash": "b092b123c7d8046443429a9cd72baa9a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/system_sound.dart", "hash": "39f5f34a4d3615c180c9de1bf4e8dde8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/tree_printer.dart", "hash": "0b59ef1fb417d687f41af0202ba86cfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/brightness.dart", "hash": "07ea273030d66ffe23789a5e62099d17"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/pinned_header_sliver.dart", "hash": "4e04af41f89adf9231bad1579f5bb9a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/visitor.dart", "hash": "9cc453290a0fea4e24b848a74967c59b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/object.dart", "hash": "daa0c9b859ed1959e6085188a703f387"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/exceptions/format_exception.dart", "hash": "2128831f60d3870d6790e019887e77ac"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/will_pop_scope.dart", "hash": "777aca422776ac8e4455ccc7958f7972"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib_encoder.dart", "hash": "d377631c264a3691da5a661b7fb4d60b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/refresh_indicator.dart", "hash": "e0b4c38191be9320c3113762d2dfebbb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/navigation/epub_navigation_head.dart", "hash": "359f8a2ff1a1d2af898350ddef0f4345"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/combinator/generated/sequence_9.dart", "hash": "e20355dd45521a6de91669be6cbfb3a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/predicate/char.dart", "hash": "e72dfdd64a9644296cdccf5ed0014b38"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "hash": "74708cb40b7b102b8e65ae54a0b644be"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/page.dart", "hash": "de67603c6b6c6f55fcd5f8b06423d29a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/pop_scope.dart", "hash": "0ff55be19444856c892e701c475b20f6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/debug.dart", "hash": "0575a78fbb39a292302737868752da77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/opf/epub_version.dart", "hash": "afca8d7be0eb483c6758867e4c6afd95"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/tree/image_element.dart", "hash": "2188f976509bff07d2e1563a116f6283"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "hash": "b1bb8356cca8b86afca314ab4898a527"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/editable.dart", "hash": "eaed941ddb98b44c090d06e0be0a7562"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/expansion_panel.dart", "hash": "5cedacfe2fd447a541cd599bfc1aef91"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/scheduler/binding.dart", "hash": "2122bbdb5de249ae3f2444fe234a5afb"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/team.dart", "hash": "f6c6b31745eec54a45d25ffe6e5d7816"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/unmodifiable_wrappers.dart", "hash": "ea7c9cbd710872ba6d1b93050936bea7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/ram_file_handle.dart", "hash": "56cd7d8ad7922478cc5edefe424c1401"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/text_button_theme.dart", "hash": "becd419f96efe14f36f18a8c8adc82cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/messages.dart", "hash": "31e2179466decb4da4d2ae1e51938a51"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/icon_theme.dart", "hash": "03d585dfc6055d74a4668e69263afa5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/webp/vp8l_color_cache.dart", "hash": "5b106eaf9bb84ebab9a0b2a82c8b27ca"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/card.dart", "hash": "90d9d45eef80ac53b194a71da4e10975"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/utils/enum_from_string.dart", "hash": "8f5d028caf602ed399219f90607e3c56"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/system_chrome.dart", "hash": "40d43557904504dbd816a205b73461b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "hash": "2ad27cdee5e6fe69626594543bd0e7c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/combinator/generated/sequence_3.dart", "hash": "9632b7c4c43e2e92f45bedc627663937"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/psd/psd_blending_ranges.dart", "hash": "7b55f87908dcbba48cc7f27d78ce47c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/image.dart", "hash": "fde04e2c536cfd70eaa5cf6000a8a7f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/utils/cache.dart", "hash": "e0cbefa359309715e5101bce98eb65e2"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/radio.dart", "hash": "9b1cee1f8aa8b638cad928232383b02b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/floating_action_button.dart", "hash": "55f7619e20765836d6d1c7001cb297fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/jpeg/jpeg_info.dart", "hash": "e74fa07316e01803f0bad7cbce31fcc5"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/flutter_logo.dart", "hash": "985cf5499dc6e521191985f55245a22c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/quantize.dart", "hash": "9fe6e4af78b9339c6823625e9b9173e5"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/autocomplete.dart", "hash": "4e8a70d478371e0d995f080a6eaa8120"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/term_glyph.dart", "hash": "1adcc56e3affffb23739c7c9d8a5fca0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/misc/end.dart", "hash": "d6b4c337633cf50449be67966688dc32"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/html_escape.dart", "hash": "efc823416c4e5e4dcced4cc2c3bbd89c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/parser.dart", "hash": "e4d5eb474812b6fb78ddb16f9ddb9472"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/utils/predicate.dart", "hash": "5cae30214f9509b4b47641f1d38b7fef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/pixelate.dart", "hash": "6526dbd1ea6e7740f31c752d1cff758b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/navigation/epub_navigation_doc_title.dart", "hash": "d7a44d8e8ae3affeccc93500126bd3f4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/snack_bar_theme.dart", "hash": "951bd729c13e8dd03a7f4edd8b10c06d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "hash": "58707cf455f97f907192b4ff92d36711"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/text_editing.dart", "hash": "9298606a388e3adb5f1bbe88ae45b1e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/utils/node_list.dart", "hash": "6b9e945de99fb44b45f72925b6e862b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/ref_entities/epub_book_ref.dart", "hash": "20fb092556ff54bbb67fdf57147c2c51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf8.dart", "hash": "329d62f7bbbfaf993dea464039ae886c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/image.dart", "hash": "4eede9144b4c0e4b14bd426654183174"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/viewport_offset.dart", "hash": "e45c87e4aadaebf7ba449f4c60929928"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/path_provider_windows_real.dart", "hash": "43f4676f21ce5a48daf4878201eb46bb"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/circle_avatar.dart", "hash": "3ad691d7f4e0dfc9bac177f56b288925"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable_config.dart", "hash": "e0f2b097829216421823bda9ec381cab"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/drawer.dart", "hash": "f26e2cb53d8dd9caaaabeda19e5a2de3"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/velocity_tracker.dart", "hash": "be0a77cf3f0463f3dacd09ec596d9002"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality.dart", "hash": "46e577ec532e21029e9cee153d7ca434"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/lib/src/post_mount_callback.dart", "hash": "c78a58fb44bcd760b18c7fd26d01d40f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/object.dart", "hash": "0cd72a3b3ab10728d2b3234014f43d83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/archive.dart", "hash": "c26dc668c2a5e8051394f89587bdc3de"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/preferred_size.dart", "hash": "dd518cb667f5a97b3456d53571512bba"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/focus_scope.dart", "hash": "fddd73db94bb2fa3a0974bed845f32a8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/platform_channel.dart", "hash": "78a0faeef5f0e801943acdca3f98393d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/combinator/optional.dart", "hash": "7d49b944ccc5ee228590126488731a95"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/hdr/hdr_image.dart", "hash": "d009f11c2c786cea0469c5ed54590dd6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/cupertino.dart", "hash": "9b83fabf1193bf4967b740dd7a2c8852"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/repeater/limited.dart", "hash": "aa439be89f7997c3c5949ce32d2486bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/annotations/has_location.dart", "hash": "f91bd03132e9e671e87f0b9066647164"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/predicate/unicode_character.dart", "hash": "d0e1db4618e688ad41ba325f1a35667e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/parser.dart", "hash": "1905c946413915323ba969930f19d207"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/processing/whitespace.dart", "hash": "e90d63df6121b610b52b7754686bc349"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/context_menu.dart", "hash": "02f1d44813d6293a43e14af1986519ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/charcode.dart", "hash": "b2015570257a2a6579f231937e7dea0e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/texture.dart", "hash": "7c07d5cc739ae29abcfbf6343ae84fdf"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/tweens.dart", "hash": "29befe23f841cf5dd2dc7df24c13d88d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/message_codecs.dart", "hash": "256d1c386e48e198e2e0a04345221477"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/scroll_delegate.dart", "hash": "e78589269f033237f43ffdc87adc47a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/combinator/skip.dart", "hash": "e0af2f414117c942cbe5e23f4f60ba3d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/service_extensions.dart", "hash": "7abc7e5212374d29bfe5372de563f53c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib/deflate.dart", "hash": "103f40776bb12911b765159ea0db7aa8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/paginated_data_table.dart", "hash": "865354d8941afe9359c093d59d7b282f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dislike/dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/safe_area.dart", "hash": "7088cc45b21c93be6b42dc748fc3a29a"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/builtins/image_builtin.dart", "hash": "d2a85c61ac6c57a4cb06d8f0bd0cebcb"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "hash": "7018ea64a9aab18f27a10711285d7573"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/image_stream.dart", "hash": "8f1d7bd8be5bc9a71d3131f835abdb80"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/list_tile_theme.dart", "hash": "822ae20c3b70355a4198594745c656f2"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "hash": "fa7ff62808b58589c2ac23ec5bdc3ef7"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/material_state_mixin.dart", "hash": "62cbf59e5c816c224ef5eaf803fc877b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/epub_writer.dart", "hash": "c3eb5c6a62243afe60ba504d4d73ef79"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/radio_list_tile.dart", "hash": "cd7a7fd807697152dfdaeb3109e4f4f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/jpeg/jpeg_adobe.dart", "hash": "f98905fdcc2b59417e62f5b22262b435"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/utils/failure_joiner.dart", "hash": "12b975946bcb9ba1b5a6dc3309a19de9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "hash": "cbeab9c259374c922b24d3cbd1cb6aa4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/progress_indicator.dart", "hash": "4f3e0e3af33c5bdfbf1d32adeba91652"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/collection/delegates/set.dart", "hash": "fa2440ff54d10a5b3255127e3b9a66ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/convolution.dart", "hash": "928754cc74b62ae804129aa3099d0a65"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/shifted_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/bzip2/bz2_bit_reader.dart", "hash": "7a47625dd33a9ef0a154e45380951aaf"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/material_localizations.dart", "hash": "063f2360bd47faba2c178ce7da715d92"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/mixins/has_attributes.dart", "hash": "1059ea9e8a0e858f944bf05dcb7b8edd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest.dart", "hash": "d623b1e2af43bcd9cde14c8c8b966a8b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/binding.dart", "hash": "61cf3ac380d43d042f8d9b7e7f6a11e6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/text_form_field.dart", "hash": "28219fbae9045c4c3217c0f3fd6fa7ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/enums/node_type.dart", "hash": "57e5dc91c30bff1774eaaa45a798d0df"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/time_picker.dart", "hash": "bf00ea3c58b6ee2b3f5422cfc3e3cd2b"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "hash": "51ed9c73dc7da80f98042b45f3267354"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/dismissible.dart", "hash": "c98d71a32518e80bc7cf24b1da6c9c57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/lib/src/scrollable_positioned_list.dart", "hash": "a39729ef10d0b2b601eeaa0311f6ce74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/universal_file-1.0.0/lib/universal_file.dart", "hash": "03735b1a4feb4ef81af44de48ce05af8"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "hash": "fab9fe32179fd0f324ced533598147c0"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "hash": "9a67635cfd2e047d996c4840d4cb18ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/tree.dart", "hash": "01d34f3007e4fddbaf4794395c4d9276"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/feedback.dart", "hash": "c8f69577793923bfda707dcbb48a08b1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/floating_action_button_location.dart", "hash": "964f3ee4853c34a4695db0c7e063eaa3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/webp/webp_alpha.dart", "hash": "00fe7d3593562041d8684cace0c2aa50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha256.dart", "hash": "1b2339e719143f3b365a03c739ab3916"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/gradient.dart", "hash": "2bc2f148be8fffe5f3a6a53fe8bc8333"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/painting.dart", "hash": "4bd60bd8ede4b9dad954493d26d3e586"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/constants.dart", "hash": "1ec635f2db97328558affe7a0c49fdeb"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/page_scaffold.dart", "hash": "805f831d339e4ab9e6b172b2bf845809"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/autocomplete.dart", "hash": "aff0bd5981a82f881b4ac72a321ee9c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/tiff/tiff_fax_decoder.dart", "hash": "1546f03d2d375952355712037edeb342"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/uppercase.dart", "hash": "997830cae101fd7a406061c7a46c5114"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/tree_base.dart", "hash": "61b8716847e9a3ca1bff526d7603b9a8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "hash": "f357bc5433a3205fc48000ad8c569c5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/psd_decoder.dart", "hash": "ef9b055b6c3ac3dccf638fd4aca3633e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "hash": "be66f00d2c9bb816f4236dd0f92bff55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/scaffold.dart", "hash": "498db9e29a08e6fdc8aee5eeb4d204ce"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/debug.dart", "hash": "1286926784ce0908d414d696a6321e9f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/data_table_source.dart", "hash": "094b2c03ad4e0ef5bc1144e281142b2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/action/token.dart", "hash": "710a4fd96b6281c1ab359ea6df4ceee8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/core/exception.dart", "hash": "7be00974229804e8ec49ca8c4fca3b5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/pvrtc/pvrtc_packet.dart", "hash": "d2611772bfbdc3d7a7e58c208959694c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/hardware_keyboard.dart", "hash": "a32174b6de983c1652638940e75aae6a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/app.dart", "hash": "d3a7a2a084a5f3901d80cef0e333d74c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/divider.dart", "hash": "6189af9ddf633811ffb6414cb9d3f744"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_exception.dart", "hash": "c39101179f8bdf0b2116c1f40a3acc25"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/rendering.dart", "hash": "4bd3950a0bf4a9f9b09f97594e363d36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/extensions/ancestors.dart", "hash": "3f842dc9d82d8b21557bf598ff4ec83b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/box_fit.dart", "hash": "********************************"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/colors.dart", "hash": "65c7fba34475056b1ca7d0ab2c855971"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/iterables/generating_iterable.dart", "hash": "1257989096cf44f0b105cfc956f21c30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/util/min_max.dart", "hash": "d6320449ba249f9242f2baa53f0d5b0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/folders.dart", "hash": "4bd805daf5d0a52cb80a5ff67f37d1fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/navigation/epub_navigation_point.dart", "hash": "d3a09daa07544c17d8bd2c0a1d0d82b6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "hash": "93c17b2980fc5498f3ba266f24c6b93b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/platform.dart", "hash": "dd109d67b92b9fbe6e0051f0c890c903"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "hash": "307c2ee6ebc77b9995c2799e8e0bed81"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/card_theme.dart", "hash": "5d8e29422039d9dcce6908b427814d80"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "hash": "bce1e8ef07d9830bbf99031d77e0b9fc"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/media_query.dart", "hash": "98cd866294c42f2faff3451e5ca74bfa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/date_picker.dart", "hash": "561522058c0ec0f631fe295300d190e6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/icons.dart", "hash": "32b222420709e8e40d12f6ea9fc0041e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/grid_tile_bar.dart", "hash": "a340eddbf129cfd60e2c67db33c6003e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "hash": "5be90cbe4bbf72b0264413e4ccb5c275"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/iterables/cycle.dart", "hash": "1696b0cbbc1cd473557f9488632bfd84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf16.dart", "hash": "10969c23d56bc924ded3adedeb13ecff"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/platform_views.dart", "hash": "1d3f3077faee6bebdc5279446f541502"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/consolidate_response.dart", "hash": "04451542afc67a74282bd56d7ee454f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/expand_icon.dart", "hash": "d6008bafffb5b2e7bf16e59a9d3ad934"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/core/token.dart", "hash": "89176d8be6120f2900340b369ce80cd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/predicate/string.dart", "hash": "a1f47bfa90f0153386bbcd0c4b16e09c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/mouse_tracker.dart", "hash": "0c402ad9ba3f3e4d7f45f24b27447ec2"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/restoration.dart", "hash": "04c713cbc0ac5e15c7978a2e91b81488"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/constants.dart", "hash": "92e6028556e74c1dc297e332b473f78e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/keyboard_inserted_content.dart", "hash": "5da306e7f2542e5fb61efff6b4824912"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/physics/clamped_simulation.dart", "hash": "5979a1b66500c09f65550fab874ee847"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/sliver_list.dart", "hash": "03001d3ddae80bbf1f35c5e70e0d93e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/lib/src/ui/epub_view.dart", "hash": "e2c901c434526112a96e53210d92ac94"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/border_radius.dart", "hash": "b75501071b7ff5d32ddab4c6ea5d2f84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/lib/src/item_positions_notifier.dart", "hash": "810497994fd5db07f51fc34944025c59"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/checkbox_list_tile.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/widget_preview.dart", "hash": "3208b2267d4d1b0d118b8fcdd774b753"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable_utils.dart", "hash": "bf850e483673d93e76e1fd5c69d8135a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/transform/copy_into.dart", "hash": "559a5e63f0482e6e095699e742384e60"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/theme_data.dart", "hash": "112daf1e5c2a46f4b457e3b76cf569ac"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/placeholder.dart", "hash": "a69e90f683dddaf61ae8d7f094219026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/writers/epub_package_writer.dart", "hash": "3967b385e5f42354f92f09a5f062b77b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/preprocessor_options.dart", "hash": "9f788a6e170d7968e9906e4d470e07f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/adjust_color.dart", "hash": "5f22a1ac9da9631abd55d2c8a0e23dbb"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/keyboard_maps.g.dart", "hash": "2c582bec6fc77f68c975f84d2252ed8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/query_selector.dart", "hash": "072bc29df9af18240c9691c60edcc988"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/viewport.dart", "hash": "c211cb790c5fc59f5bb6dcd61e0abcab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/jpeg/_jpeg_quantize_io.dart", "hash": "71c7dc8e76417c6525a7dcf2ff53d1d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/builtins/details_element_builtin.dart", "hash": "50806acc49235d171f461c2597bb0034"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/path_provider_windows.dart", "hash": "38dc31b8820f5fd36eedbf7d9c1bf8d9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/text_field.dart", "hash": "b0f444b219eafe3ec2bb9e8a09e545f6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/shader_warm_up.dart", "hash": "6d0b38802aff8cbe310e72f1a62750d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/interface_level.dart", "hash": "1bdb47a9af4b0a5d759937da8ff04db0"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/performance_overlay.dart", "hash": "c5e44030289c2c25b26c5b3aa843b3cc"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/title.dart", "hash": "e556497953d1ee6cd5d7058d92d4e052"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/entities/named_entities.dart", "hash": "c7e489fa5d00c1717fe499f3845c2abb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/events/doctype.dart", "hash": "c2d76b78fb107e358b1ad967f15f1746"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/menu_anchor.dart", "hash": "ceafe3fee68e6597afe301af3cc318c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/tiff_decoder.dart", "hash": "82fe3ce0cf70eebd4092f250ee23cf78"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/scribe.dart", "hash": "d195153a8c01a0392b38e3b9adc672d8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/recognizer.dart", "hash": "990244fbee5d6f551e98a4bcce092389"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/scheduler.dart", "hash": "95d8d1f6a859205f5203384e2d38173a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/emboss.dart", "hash": "b1de687397537506ecbc136f771d7c2a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/range_slider.dart", "hash": "2e0b7bb9c12ed9f989240a20a878badc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/contrast/contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/toggle_buttons.dart", "hash": "64a2ea17e8058aec30096102af030f98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/_crc64_io.dart", "hash": "f6e82da324f67628362bc63ed3314081"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/navigation_bar_theme.dart", "hash": "b5eb2fd4d6d9a2ec6a861fcebc0793d2"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/spell_check.dart", "hash": "24094ce9de1b9222a8d6548d3c01045a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/action/map.dart", "hash": "822f0a79dfd6a3c997d2b898ec420b97"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/multitap.dart", "hash": "578ff911d6e70b239fd629f5a0206fd8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/bottom_app_bar.dart", "hash": "fa60d1a6f81796232bc16dae4ed5f4ac"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/licenses.dart", "hash": "c0cf85f80b79542d2b0e1a00547d7310"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/date.dart", "hash": "f36568b4288388242cb6f7775cb60c42"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/theme.dart", "hash": "a02235e1a98989d6740067da46b4f73d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/desktop_text_selection.dart", "hash": "dd3402d5403be91584a0203364565b1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/css_parser.dart", "hash": "34bafb0912678dddfb242236a2ae7e9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/tree/interactable_element.dart", "hash": "ca865ea962573ef0479f376fc624ffe4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/orientation_builder.dart", "hash": "177fda15fc10ed4219e7a5573576cd96"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/webp/vp8_types.dart", "hash": "f1eb1fa9b2d866bdf3ae182b6f026be0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/exr/exr_attribute.dart", "hash": "9cbf9c7afa42314193a1e1bd5bbf2cc1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/constants.dart", "hash": "be94b8f65e9d89867287dabe5ea1dff1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/menu_theme.dart", "hash": "89ae530b1eb1ce798ec54bc9b45efdba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/highlighter.dart", "hash": "5265b4bdec5c90bfd2937f140f3ba8fc"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/proxy_sliver.dart", "hash": "1244032abcc6103795809163331238a9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "hash": "35e99597a2bc1839b114f890463b5dad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/style/padding.dart", "hash": "c46e25a0b090d280389322cfabdba2ad"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/blend/blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/tap.dart", "hash": "2d638931b01747be8315be89cd473caa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/entities/epub_byte_content_file.dart", "hash": "19abc7562bfc95fde9087560537c1c0e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/oval_border.dart", "hash": "c8a14f8ecb364849dcdd8c67e1299fb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/writers/epub_metadata_writer.dart", "hash": "e7ae32e7a0b1cd76b7f050f0150d45f9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/scale.dart", "hash": "abbe93b36782df11e43e348dadf52e94"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/constants.dart", "hash": "c7cc72c1e40d30770550bfc16b13ef40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/css_printer.dart", "hash": "9a6fff298db26d4e059ebb664863ab18"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/button.dart", "hash": "d7a239f8b80f844857527c2012e4fa1c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/modal_barrier.dart", "hash": "830b9f37313c1b493247c6e7f5f79481"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/debug.dart", "hash": "6f516ffde1d36f8f5e8806e7811b15ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/entities/null_mapping.dart", "hash": "4bc463f9c4b5496d8918b58070c10515"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/jpeg/_component_data.dart", "hash": "baf4911922fe0f6c9233888aa94c1432"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/gif_decoder.dart", "hash": "245313ac00f1889573504a4da4810cec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/hdr/hdr_bloom.dart", "hash": "340e9330fdb69983489d2fcd1bbb27f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/fonts/arial_48.dart", "hash": "9111f51295740c21166a9c1aa3bb4230"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/physics/utils.dart", "hash": "727e4f662a828d4611c731f330a3d79a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/matcher/pattern.dart", "hash": "2108c716fd8198fa3a319a1ec6cadc9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/transform/copy_rotate.dart", "hash": "4ddd50492e3029d4bc427f9aa653763f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/draw/fill_flood.dart", "hash": "dcd5239d3a4ef46ca76d7272db11c1e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash_sink.dart", "hash": "ec5409b8e30f22b65a7eee1b00a12d06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/matcher/matches/matches_iterator.dart", "hash": "4c92351d347c52a00797317aa487600f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/psd/effect/psd_solid_fill_effect.dart", "hash": "acc73ca9c6c2b95f4c68a78e291199de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/readers/root_file_path_reader.dart", "hash": "8b5b95c3ef802a2fe8f6952ddbadcc11"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/drawer_theme.dart", "hash": "62b4a318d3ec0d03d3dc78b84cf0458a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/app_bar.dart", "hash": "7706f479f74f6076ef8113576fe54749"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/treebuilder.dart", "hash": "2c8ef2ed22dd79552a4d286b31817a27"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "hash": "9645e1d88d63387bb98a35849f4cbe53"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/repeater/repeating.dart", "hash": "9a0bdbeb6a1452722cc91b80ee779998"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/app.dart", "hash": "7a7adcde254b7966b08a01e932438be4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/text_editing_intents.dart", "hash": "47ccb32c843b4075a001e612853b2a31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/opf/epub_metadata_meta.dart", "hash": "ea30e326e284e1836a1ab881331237bf"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/tooltip_visibility.dart", "hash": "ee2f417f35b5caa4a784b24c1bc32026"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/pages.dart", "hash": "068ea69f3733bd1aa72b910e51b41b12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/predicate/lookup.dart", "hash": "f2698cdf4b07ce88e4996e23f26cd0da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/draw/draw_string.dart", "hash": "516e2cf153b9442b74ac2daefe299c63"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/nodes/document_fragment.dart", "hash": "88acdeb4b5b5a9e5b057f7696935fc2e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/strut_style.dart", "hash": "ee62fb3be5d885d65054fac4b84cac6c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/tiff/tiff_info.dart", "hash": "85f13d0976e7a41c07aa326053038bd3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/extension/helpers/image_extension.dart", "hash": "b98ee639e06dbf710dfacac291884aad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_queue.dart", "hash": "d6f045db9bd5b72180157d44fee9fbfc"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/isolates.dart", "hash": "1dab3723527db6a19410ed34b6acaeed"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/scheduler/priority.dart", "hash": "ac172606bd706d958c4fe83218c60125"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/lib/src/data/epub_cfi/_generator.dart", "hash": "c8df9069ccf6882a02e0a10f7032d214"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "hash": "c06267b6c315a5e40f28feb6019de223"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/combinator/delegate.dart", "hash": "5fc1a25f60cfa0a0280878377348c63c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/tiff/tiff_bit_reader.dart", "hash": "3311b159510a4d804456069fd412eb9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib/inflate.dart", "hash": "2f1d6a5b171d0ded9b5984be4c426d4a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/context_menu_action.dart", "hash": "84f94e87e444ce4ebc562b2707348a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/definition/reference.dart", "hash": "1253a1a49f9d6789e547fbb5a9301d3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/util/octree_quantizer.dart", "hash": "5a7ed4cffee6c4e8547f73c2d8c4a456"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/circle_border.dart", "hash": "a2aa815908f2e15493e374b9380e558a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/ink_decoration.dart", "hash": "a2ab6e0f334e5a28af29766b82f7f4b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/pvrtc/pvrtc_encoder.dart", "hash": "2abae040e0fb5aa1f780cd1891ab9221"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/processing/befores_afters.dart", "hash": "ba97af2aa9d323e58ecc4fcd36779ae9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/text_button.dart", "hash": "dbbc7f46620d816e615bbbe67eb258e7"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/image_filter.dart", "hash": "6c0e97a3b04c9819fe935659014f92e8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/stepper.dart", "hash": "56198ea7cfc4930ad8bcfc81a2061b78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/expression/utils.dart", "hash": "8608f71f077e370ee14d37c711e6580e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/psd/effect/psd_inner_glow_effect.dart", "hash": "4ad16c49081a85e09b4beb00afedb4eb"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/button_bar.dart", "hash": "42c4c0281ec179aea5687dbced56aca7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/navigation/epub_navigation_page_list.dart", "hash": "c70f6f9e7a741e4405a58dacc5f06e75"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/image_decoder.dart", "hash": "eaf5aa7cf4fe19db30724f637b38257a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/time_picker_theme.dart", "hash": "b269f9d6378b540b7d581db466ad98d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/style/fontsize.dart", "hash": "f1a9a2f94e067796fb2704af5f0b62e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/event.dart", "hash": "1a7fe7a35dbd168a7f2e10065f4a3158"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/mouse_tracking.dart", "hash": "5da121a0d3087e7cf021bfcdeb247b77"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/scrollable.dart", "hash": "c8260e372a7e6f788963210c83c55256"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/ico_encoder.dart", "hash": "4a49e6ab48ef3c0be6f4c4a0ece4e13e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/LICENSE", "hash": "612951585458204d3e3aa22ecf313e49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/banner_theme.dart", "hash": "355538055d623505dfb5b9bae9481084"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "hash": "1303bc77ad63625069f2d23afc73f523"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/mixins/has_parent.dart", "hash": "7f47dda6ed10e33236d465680dc8c12b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/LICENSE", "hash": "06d63878dac3459c0e43db2695de6807"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/events/declaration.dart", "hash": "3cf7786074ce9f1e148fe5f4a60479d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/exceptions/type_exception.dart", "hash": "d39becdaf9cc42e3efd0c9cdf0034ac4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/extension/html_extension.dart", "hash": "d8d80dfbd48c6e640c286c68012403fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/exr/exr_rle_compressor.dart", "hash": "3cb3ef47fe2e1faf9772e986fc50af1b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/gestures.dart", "hash": "55324926e0669ca7d823f6e2308d4a90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/utils/named.dart", "hash": "7d9a75e0bd8e5c9b49ad6c0816666b4a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/image_resolution.dart", "hash": "0f2a1a61119c0bef3eaf52c47a2ebcf4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/navigation_drawer.dart", "hash": "7755bff1bceea0db42330320ad10baad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/pvrtc/pvrtc_decoder.dart", "hash": "7614e851370290b9fa2dcb7f783491cd"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/debug.dart", "hash": "dbb0bb20c79bcea9397c34e3620c56c3"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/overlay.dart", "hash": "cd0cbb4d29516ed6b03d1c68f0c08477"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/gaussian_blur.dart", "hash": "07f6cf3f5f9860e8e0870016601db312"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/opf/epub_spine_item_ref.dart", "hash": "e8322c9a185e0ae7de0508e5efe68846"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/predicate/converter.dart", "hash": "affb97b6cbd84919fa30ea3bcd5f12df"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/service_extensions.dart", "hash": "eb115c2e8f0ff170bf26a44efd1b5c05"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/progress_indicator_theme.dart", "hash": "377731ed35ad8d1d36dcfd532a3d308e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/sliver_tree.dart", "hash": "d99e76320b224b4518e76f311ef4a804"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/ink_well.dart", "hash": "38df6f8cafb853c1acf0f6e6a4b4950c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/combinator/not.dart", "hash": "6bb47d3d823202b76bef61c1ccce067c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/style/size.dart", "hash": "404852cf2127fe01b769e12895be741f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/exr/exr_compressor.dart", "hash": "bf71db2307ca3fe447fb871b32b0a58a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "hash": "9ec81b597c30280806033b70e953b14c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/animation/animation.dart", "hash": "c8564aa311746f4047cd02e26ff4df75"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib/zlib_decoder_base.dart", "hash": "1843b987225d300957fc26c92d434833"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/word.dart", "hash": "27756cabcc328c2f7ae9e353b339d89a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/text_scaler.dart", "hash": "b6e992b1127f8376358e27027ea7a2ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/writers/epub_guide_writer.dart", "hash": "36b8f4e48a815603f7db1df2b45c7c02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/.dart_tool/flutter_build/dart_plugin_registrant.dart", "hash": "20172799f517b2270793ab868b8a1544"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/psd/psd_mask.dart", "hash": "7e4f9d47b668c76a73d548dc9f00beff"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/routes.dart", "hash": "33adcae8de663e2e8f8f410da7fc8023"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/ink_splash.dart", "hash": "31b0d2bf647a0ce615f4937dd5307b1c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/system_navigator.dart", "hash": "0db5f597f1cc6570937e6c88511af3a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/adler32.dart", "hash": "fc03346c11301342d64a349d5c317c7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/opf/epub_manifest.dart", "hash": "8370301ed4e00f0e2b152f8e16650f1d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/webp/webp_info.dart", "hash": "b310b5a966e31410665af1fb0c7b4e4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/bzip2/bzip2.dart", "hash": "c452407613e1b5e2deaba24159998ced"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "hash": "0bc495ddf9b02a06a5fc6934847e8708"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/path_provider_linux.dart", "hash": "b48ba72a2d5d084d297c3d78e351036e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "hash": "04c960ae6d770135bb0b6acf14b134a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/lib/src/data/models/chapter.dart", "hash": "98f1b604cbe0fa47d6f86f27bde8d00a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/webp/webp_huffman.dart", "hash": "67f4014042b9020c882a5b8a4a35fef6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/encryption.dart", "hash": "5a53fc34e740ca261be6ebb21b62268a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/mixins/has_children.dart", "hash": "7c666bff17f2cfae821f93f0c5e66a64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/misc/epsilon.dart", "hash": "b9283cabc57ae94b3c75f147903751fa"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "hash": "a6d730f196620dffe89ac987b96ef6c3"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/chip_theme.dart", "hash": "525e57b6ade38da2132c8ddb0ea78547"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/long_press.dart", "hash": "c97a8ffd51479d05a18a54ac27ccba15"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/app.dart", "hash": "ca378f8a4dc93cea9ab759f410dcfdb6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/matcher/matches.dart", "hash": "5ba6e004392bbc498c40ccb026b0a845"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/ref_entities/epub_byte_content_file_ref.dart", "hash": "3c7d45777592ae8523aa8d68f4bf724b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/action/pick.dart", "hash": "c60b204fb5e7d501c0addb330c88d2de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/nodes/attribute.dart", "hash": "9554d9749364a5e33fc853c08b09f076"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/extensions/sibling.dart", "hash": "6cee72f673d593b0b84628bf243727a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/opf/epub_guide.dart", "hash": "2fcc3183d3331ac94af8afedc44f2de6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/reorderable_list.dart", "hash": "67241b28b6ab2188280fb614f1607b2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/utils/conversion_sink.dart", "hash": "efcbc6fd4212ea81281561abddbf29f9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/collections.dart", "hash": "f209fe925dbbe18566facbfe882fdcb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/petitparser.dart", "hash": "6cb32004f228090f1200484076254c7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/lib/src/item_positions_listener.dart", "hash": "c5d9627f04b94943263440e248d9c6d7"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/widget_state.dart", "hash": "3c24303086312d7181ffa10d0521029a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/psd/psd_image.dart", "hash": "797828a947b9065394678038db3d6f14"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/snack_bar.dart", "hash": "5c5a8f737a2cec1d969f4a9f8dc80a8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/writers/epub_manifest_writer.dart", "hash": "df775053e7d16022f94c75637bc172fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/core/optional.dart", "hash": "3008bb2699e1663c989e18f97e6ec94b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/lib/messages.g.dart", "hash": "414fcae87c705a9820e16d8f7b40aba0"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/_web_image_info_io.dart", "hash": "e4da90bb20b3980a03665a080c87a098"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/events/end_element.dart", "hash": "813218451c1d8dd310e1233bd4ca7a4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/expression.dart", "hash": "79503c7448238b77502c169788e26dbf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/predicate/lowercase.dart", "hash": "e2bcdfd80ddc73e02b457e8544242028"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/banner.dart", "hash": "674ba42fbba2c018f6a1a5efd50ab83e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/editable_text.dart", "hash": "20b03effe92fdb82cb2b1efcf637be3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/action/where.dart", "hash": "30325626c486da5b0b5e6ca9d6a6d337"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/.dart_tool/flutter_build/39f1ba25fd9848056bef5babb95f5ba2/app.dill", "hash": "6aae6e13d1c46ed5f772f06e3cea93fc"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/geometry.dart", "hash": "9e353a749332f6cfdbe6f0d07ff17f5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/combinator/generated/sequence_4.dart", "hash": "ba02460ed2591611ff8506bdd88f569e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/box_decoration.dart", "hash": "********************************"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/drag_target.dart", "hash": "166147b7bee5919995e69f8ca3e69d17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/expression/result.dart", "hash": "bc503b6c5e3658a13efaee4e0638935a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/slider_value_indicator_shape.dart", "hash": "949350c1ca059ddb517d7f4f80b21ecd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/extensions/following.dart", "hash": "7f4a5458515781cb38e39651bfdd2f04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/repeater/separated.dart", "hash": "70a35c4e76561a207bce18013ed087c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/iterator.dart", "hash": "dd8517aec9099740b2b5828bde8d33aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/ico_decoder.dart", "hash": "150931ef432d6f6683f4a6842a67e167"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/codec/event_codec.dart", "hash": "3a9a69af68cc0a35c422d0bf03873265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/jpeg_encoder.dart", "hash": "d25723b3a6223b737cd34a98e19a68b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/iterables/partition.dart", "hash": "0dfaf29d73d72366a0e9a0a5ed13c3e6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/calendar_date_picker.dart", "hash": "224c14ef0447e287cbae1b7aed416290"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/layer.dart", "hash": "659b88645890c6437ea5ce4928e8871e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/icon_data.dart", "hash": "eb9b3bf513b18ddaf0057f3877439d9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/utils/optimize.dart", "hash": "ebd9dcbeebab7ad717e6f7efb6a47f0f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/exr_decoder.dart", "hash": "b5615d0baf6365ff84fb26c037e49cd5"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/undo_manager.dart", "hash": "0821fcdff89c96a505e2d37cf1b52686"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/thumb_painter.dart", "hash": "e37bb4fabbf2e61e9b7fbe06f5770679"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/shape_decoration.dart", "hash": "6486bc074c81ec57bdafc82e6a64683a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "hash": "55b4fed5dadc735394ecc0e13867c2eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/webp/vp8l_transform.dart", "hash": "ccce8d0180d7d10fdb1e6c015029771b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "hash": "8e870f9527626d34dc675b9e28edce85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/bump_to_normal.dart", "hash": "034f724aa885a1534e3604180c67150b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/pvrtc/pvrtc_color_bounding_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/top_level.dart", "hash": "15439eaa12b927b0e9a42b9d168e3371"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/internal/clamp.dart", "hash": "bbe5502a8cb0f93be088334a3b98edbb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/bmp_decoder.dart", "hash": "d1f388ddcf6f4f67d01fb51dcd0025a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/epubx.dart", "hash": "eb6542fdcab75f80ea1fff70acc5863b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "hash": "262d1d2b1931deb30855b704092d3cb4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/dialog_theme.dart", "hash": "8383986e94be1a258a59af29b9217876"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/writers/epub_spine_writer.dart", "hash": "d302aed2e1e088575c171ec099b03038"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/list_counter-1.0.2/lib/src/counter.dart", "hash": "34d52814826bdf2d24fddb1cba0c9064"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "hash": "77e3a9ed54e0497465a4346f273bcccf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/byte_order.dart", "hash": "a13e91c8cc53cc5cf91fc5ec7877f9fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/parser.dart", "hash": "ed6e10b66c408845188f75959c15a23b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/time.dart", "hash": "872d879ea43b6b56c6feb519cc12d5a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/xz_decoder.dart", "hash": "c65d4cb52a62688ccb3b832f190386c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/bin/cache/pkg/sky_engine/LICENSE", "hash": "10f2d960c7d6250bbc47fdf5c6875480"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/lib/src/data/models/paragraph.dart", "hash": "32fef8a63f321bd872755d562362bafc"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/animation/tween_sequence.dart", "hash": "eabd3dc33b1a3a2966fa68f6efeb6bce"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/raw_keyboard_android.dart", "hash": "c9111e47389ee4b70aab720435a2a2df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/style/margin.dart", "hash": "ae2ce2267d1d4be4caa00bac1eabc055"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/annotations/has_buffer.dart", "hash": "22acb270c1bb267ee16b3d64a3faa825"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/internal/internal.dart", "hash": "ef5a4dd85c44d5a5a1b782d908512ae7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/exr/exr_zip_compressor.dart", "hash": "74b8e818681706d8afab6afb45f60def"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib/huffman_table.dart", "hash": "61d730712ef42fa468853460c529f0a2"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/dart_plugin_registrant.dart", "hash": "44b8efa69ec831d1a0ce74c20ecc27b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/combinator/list.dart", "hash": "69c4980a512a91477aa1a6289583342b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/icon_button.dart", "hash": "5d99a505ddc69d5accc4e5a83f5cfa4d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/scheduler/service_extensions.dart", "hash": "f49291d1bc73b109df4c162db10003d2"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "hash": "1ac31829939c22fe50a4a95a77f84d59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/action/cast.dart", "hash": "dc379ed249557649f50b9c27d0033be6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/debug.dart", "hash": "51fa10cf30bde630913ff4c6e40723ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/iterables.dart", "hash": "4d65379c84f775b31cc9ce1c7d06ccda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/browser_context_menu.dart", "hash": "db4a14227247e2524e46f6b0dd9da267"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "hash": "c679063104d2f24639459c8ab3eed77a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/hdr/hdr_gamma.dart", "hash": "e7dfb7b86454943b799868b9fb7821d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/builtins/interactive_element_builtin.dart", "hash": "208358f11478abeea8d3d65a83bfe868"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/list_counter-1.0.2/lib/src/int_range.dart", "hash": "8275b27c111a9b0e244ada97dd2820a5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/png_decoder.dart", "hash": "0766f9d1593a021a8e5f68e69a22d650"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/animation.dart", "hash": "c4838a7db08a7521bad7a1d5b532ddd1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/scheduler/ticker.dart", "hash": "3e8df17480fcb123b3cdc775ca88dd89"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/annotations/has_parent.dart", "hash": "a7ac3293430577fa9c028b0df6607fa4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/raw_keyboard.dart", "hash": "02dabe6a8cd832d69b4864626329ef30"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/animated_size.dart", "hash": "91d8303ca1ccc72eccc1ae636c7825ed"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/matrix_utils.dart", "hash": "59475498db21e2333db54d6478af7c94"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/text_boundary.dart", "hash": "501bafdb6d3784f18f395d40dfa73cd2"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/nav_bar.dart", "hash": "8e471191ea3b6cdd6c970bf5be4cc86e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/processing/relative_sizes.dart", "hash": "9ca8dce4c5ac3449db91a4c5be0743aa"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/sliver_group.dart", "hash": "630fe5f86ee37699c534f9c91f21f03c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/entities/epub_book.dart", "hash": "9de4b301a78b442e58afff660ccf9450"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/slider_theme.dart", "hash": "86d7d305c24e6073b89718914fcd3ee0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/builtins/ruby_builtin.dart", "hash": "769b2647b09f1510b0ccd148d74bb181"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/comparators.dart", "hash": "8ac28b43cbabd2954dafb72dc9a58f01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/jpeg/jpeg_scan.dart", "hash": "052143cc1a8da36bbb5fa9a6fa7e7ff8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/gif_encoder.dart", "hash": "1cfb52860b7dfefd5162a14bf30dbdae"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/sliver_fill.dart", "hash": "123520ee3a48eebf4ba444e93436bb1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable_mixin.dart", "hash": "0f5d8dd74761633229f5cf2fd6358e05"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/search.dart", "hash": "66a927b3f610db5ff8c77a6ba3ccee0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/table.dart", "hash": "1f437276972808bf4cf722440da1b231"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/about.dart", "hash": "4bf9cb0fbb8b0236f0f9e554c7207a4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/epub_reader.dart", "hash": "72220db05fb7193bb506841e72441832"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/text_formatter.dart", "hash": "b139a58dace0b9d9a07a3523ed72ced5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/collection/multimap.dart", "hash": "128f7c526c21c3eede1e8aca83cbe779"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/animated_switcher.dart", "hash": "008b3ea4691331636bbea9e057357ceb"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/text_span.dart", "hash": "6fc640633e357a75291efec1c68b02ce"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/checkbox_theme.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/entities/epub_schema.dart", "hash": "bb70c75e4cd89942857a620e983cfa31"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/implicit_animations.dart", "hash": "c9105f08cb965dfc79cdbe39f062d6c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/utils/zip_path_utils.dart", "hash": "e274f16d9e48156b1874687cd98f2a16"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/form_section.dart", "hash": "cd995d0f309bf74d0bbe94eb1e4e8e81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/nodes/doctype.dart", "hash": "a0ff9321b483226cdbe4773e33779715"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/matcher/pattern/parser_pattern.dart", "hash": "79a5f25a1a9d4aa4689bf37171e1b615"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/normalize.dart", "hash": "7124cef955e7ab87e3ac3d7329ef2b5f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "hash": "********************************"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/sliver_tree.dart", "hash": "5cbb66bc2f7ff989a32bc1e5ce5971e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/nodes/data.dart", "hash": "d7fab9eeba6ce2b3fae0a93d5622ac93"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/token.dart", "hash": "f81e0f51e6529eaf92d4e8d6196e4e13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/crypto.dart", "hash": "3b0b3a91aa8c0be99a4bb314280a8f9b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/system_channels.dart", "hash": "b3d31c9c130a73d5425905f361f63957"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/converters/node_encoder.dart", "hash": "af7b5d8de0c9d9df88cdffcae9d7c959"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "hash": "28d3a26c44687480bac3f72c07233bf6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/font_loader.dart", "hash": "a29f0df228136549b7364fcae4093031"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/overflow_bar.dart", "hash": "d2694042e337ac1f2d99602c25be195a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "hash": "d390b15ecef4289db88a4545e359bc8a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/nodes/cdata.dart", "hash": "008d33cc2aea11e7921ee238469947b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/any_of.dart", "hash": "853db49f6cc034267b3dffc26052f4aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/canonicalized_map.dart", "hash": "f5e7b04452b0066dff82aec6597afdc5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/bitfield.dart", "hash": "d33374c0857b9ee8927c22a5d269de9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/misc/newline.dart", "hash": "d6beb013c7ba06cf6076e547a7b21f1f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/performance_overlay.dart", "hash": "3d892f04e5e34b591f8afa5dcbcee96d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/custom_layout.dart", "hash": "dc552952c58db02409090792aeebbdd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/sepia.dart", "hash": "7c93471b4f7b95b5f43c9c351de7fdf8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/anchor.dart", "hash": "b90eb0f011eb7730ebbd4c6986cc47dd"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/date_picker_theme.dart", "hash": "3ab9652d1101aac3b5d74a4495d860ef"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/popup_menu_theme.dart", "hash": "384c15d93757a08ae124e6c2edeb4e9e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/flavor.dart", "hash": "912b76b3e4d1ccf340ee3d2e911dfd28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/lib/src/ui/actual_chapter.dart", "hash": "17b7bf7877e9da46d2625d75611c9ffa"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/input_chip.dart", "hash": "14177be7a74b321668af2b9effa0f396"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/breaks.dart", "hash": "73189b511058625710f6e09c425c4278"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/transform/copy_resize_crop_square.dart", "hash": "d2abfec7f0a972d8a7dbde04ee30c7e9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/text_layout_metrics.dart", "hash": "13be7153ef162d162d922f19eb99f341"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/utils/list_converter.dart", "hash": "5f5f3a1074f40b8fc37c2b3ba5ec0432"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/util/interpolation.dart", "hash": "e25ca3c7612aa726eacefe01e52735ac"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/spell_check.dart", "hash": "e3d917994e875601c2dadaf62de546f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/tree/styled_element.dart", "hash": "b7e932901953547f29510f341732bef1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "hash": "a0e89676ccae6cf3669483d52fa61075"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/visitors/pretty_writer.dart", "hash": "09214b5a4ed4e104f212ef38f676fb1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/guid.dart", "hash": "55bb53dd4f9ed89c9ff88c204b59293c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/readers/content_reader.dart", "hash": "11c72b96ef1a06674ffb11cc7d3e0cb2"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/.dart_tool/package_config_subset", "hash": "b25cd1feaaf7cda7838c9bfaf24e5d78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/utils.dart", "hash": "7014dc257215cc17a58e5bf88855eff7"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/slider.dart", "hash": "1ae1a412c9f9daff34b9dd63e60cec2d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/switch_theme.dart", "hash": "a88d8ea7c8c98dd1d35ad2853f04efe1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/definition/internal/undefined.dart", "hash": "bb00c98e50d3c71d4ab7ac7c46122f3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/iterables/infinite_iterable.dart", "hash": "8ab6d645929a5bcc4263a0498f73f361"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/repeater/character.dart", "hash": "ddce6034695da8c5dc36994409d26189"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter_tools/lib/src/build_system/targets/common.dart", "hash": "5bfd685ff7be85d072b26c7030af999f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/divider_theme.dart", "hash": "04f538d5fc784c89c867253889767be4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/button_style_button.dart", "hash": "6a7d9ee6c8fae5e9548911da897c6925"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hmac.dart", "hash": "2b5fbc54f77ca9c1e5ac90eb3c242554"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/view.dart", "hash": "15957b9d3eac4a2e1acaa24a3032afe7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/lib/src/viewport.dart", "hash": "124c5e0ed13494dfaa1e4a1d860d88d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/visitors/writer.dart", "hash": "a8499171c0b67ca96b9f8b0462e1079b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/util/input_buffer.dart", "hash": "c300336a40b94f8d6a48b5788ec0f2bc"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/selection.dart", "hash": "cc4a516908b08edff4fade47d6945e5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/psd/effect/psd_bevel_effect.dart", "hash": "793fe012ca3ce7197a3af84a51588723"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/capabilities.dart", "hash": "5fe5b5ed3ec92338a01f24258b6070a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/archive.dart", "hash": "7008ef5f2a7d0e38ee2fbaa51a89ef24"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/haptic_feedback.dart", "hash": "9ea1746a0f17f049b99a29f2f74e62ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/src/iterables/merge.dart", "hash": "65d440e10940c9252d5f6c7b99439efe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/webp_encoder.dart", "hash": "d713ff3cb30b3f7cb0e5f73a34caf8bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/archive_exception.dart", "hash": "961bcf3872c8a50d7233563a0f5228ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/universal_file-1.0.0/LICENSE", "hash": "1a6d82b1e8aaa841cfab27a0acd3179d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/curves.dart", "hash": "4aeb4635d84df42e6f220aba366af7d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/list_counter-1.0.2/lib/src/system.dart", "hash": "7f823ae1d362fb149064c2defbd72222"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/list_proxy.dart", "hash": "d1c07a46157914ec4aaa9aa9a957df37"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "hash": "e8aae4779eccfdedd9c4b8cbce4ab952"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/date_picker.dart", "hash": "15ee790ce6b1c0a29d38af8094ad1722"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_mixin.dart", "hash": "89dc3f84db2cd1ea37e349fdb1de09bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/style/marker.dart", "hash": "ee5a9e9c7cdd4302cb73fb4768881efa"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/badge_theme.dart", "hash": "e1a148a465b713a6366d5a22a1425926"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/none_of.dart", "hash": "9f69b819f3943f691b452d84d4cdb609"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/sheet.dart", "hash": "e88cac3fc4dc6a17d2bd13549d433704"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/xml.dart", "hash": "fcf0dfcafac17dc3ed539b4587340320"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/flutter_logo.dart", "hash": "044d6bef26a97ada1d56ff6fe9b7cc14"}, {"path": "/Users/<USER>/Desktop/test/my_flutter_app/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "hash": "85aa53b038be894edc8ed4b952643c56"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/image_cache.dart", "hash": "4a2215ab704d09e97121c1bb71942b3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/LICENSE", "hash": "34ae61d32ea6f4a2c632b6b510e5ef71"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/table_border.dart", "hash": "bbc7eccdbd8472a2180e0dffce323bb9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/localizations.dart", "hash": "9c051d9a4098051ba8258eae9aae3195"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "hash": "aef544fef0ced7679e0edaf5f8d036b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/hdr/hdr_to_image.dart", "hash": "e0bc277a6902506c42becc634cffa344"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/combinator/choice.dart", "hash": "404ec528c031ebc7486f12477b06de28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/events/text.dart", "hash": "f52860ffbd4c6858f092292d1589d556"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/tap_region.dart", "hash": "96b4be28e9cb48156c65de35d7ccefba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/combinator/generated/sequence_5.dart", "hash": "1e4da3528271172cb17b59a72a37a57a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/linear_border.dart", "hash": "0fa4800227413041d2699ed47918c7f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/tga_decoder.dart", "hash": "e477e10e54994462e76a9421047ddedd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/opf/epub_manifest_item.dart", "hash": "ec57feb7826bc37395d6e776c7da1710"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/lib/src/scroll_offset_listener.dart", "hash": "c8b1e1b77a969ac1c238b944f5f65a91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/collection.dart", "hash": "2c60afa3900ff79306854c7d290c78e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/letter.dart", "hash": "3b849eb1eb50df2663eeecd3801e3193"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/builtins/vertical_align_builtin.dart", "hash": "63add762dbb517086941950470a539d1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/segmented_button_theme.dart", "hash": "b815d11a718e0a4d6dec5341e2af4c02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/matcher/pattern/pattern_iterator.dart", "hash": "accb24637ddbe55d7a3f76e4618bdd22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epub_view-3.2.0/lib/src/data/epub_cfi_reader.dart", "hash": "5270c5be6e5cae3eafd6b89787255158"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/semantics/semantics_service.dart", "hash": "fbfdd6181c7ea8d5950c24b467debf31"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/two_dimensional_viewport.dart", "hash": "7bdfcadf7dd131e95092d30909e5b11f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/reorderable_list.dart", "hash": "a101af17dcc01da8f97ef55242f0f167"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/icon.dart", "hash": "826b67d0d6c27e72e7b0f702d02afcec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/webp/vp8.dart", "hash": "45bdbc71051c9d76cc401896daaa6572"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "hash": "98772211ffa69a8340f8088cd7193398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/events/comment.dart", "hash": "74fb000405fb96842a3ce15a519d8ae8"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/drawer_header.dart", "hash": "f996ce49eab57718350b84e11ea3192d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/dom_parsing.dart", "hash": "723a3d6fbd3de1ca1e39b70c5ddb5bcb"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/predictive_back_event.dart", "hash": "16859f5e798cf33fc3c76a7a3dca05d7"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/focus_traversal.dart", "hash": "fc0c77cc9957db2d82d3e8d56f8ef9d9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "hash": "f7b9c7a2d1589badb0b796029090d0d5"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/sliver_resizing_header.dart", "hash": "eca5aa939aa9722ead4b6c347fb4d11a"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/scroll_physics.dart", "hash": "f26f519ea124441ec71b37df7cfa1ee9"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/pointer_router.dart", "hash": "8c1a2c1feaeb22027ba291f1d38c4890"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/layout_builder.dart", "hash": "f5dab330de9938d8ad99263892810f3d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/expansion_tile_theme.dart", "hash": "045c779ec8564825d7f11fbbd6fb2fa1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "hash": "2e074f4fb954a719546377c67cb54608"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "hash": "2a374faf6587ee0a408c4097b5ed7a6e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/radio.dart", "hash": "9802442b82d3be84abecae8d0a2c7bd6"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/foundation/_capabilities_io.dart", "hash": "faf4d014b3617ede3150f80eba25e3b4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/asset_manifest.dart", "hash": "a2587417bcfd04b614cac5d749f65180"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/opf/epub_metadata_contributor.dart", "hash": "1391109929e38ef597366e1a4e19904c"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "hash": "e5b4b18b359c9703926f723a1b8dd4ac"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/sliver.dart", "hash": "ebd06d8f4cce7c59735a2ba28d6dba97"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/sliver_fill.dart", "hash": "6987c3474a94dd1c4ff8f8540212f16b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/streams/subtree_selector.dart", "hash": "ef93f78a8a380eeade385040b1d075c7"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/text_theme.dart", "hash": "f60846aa76dab98607aa06c9bd6cf1dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/extensions/nodes.dart", "hash": "8608080cdfc143d462b0f9947dc0d7c1"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "hash": "ccb3c80f13485133893f760c837c8b62"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "hash": "9ad11b4bdb179abe4ccb587eb0e2aebc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/shared/types.dart", "hash": "7e327134a49991d7ba65bbfe46bb8f4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/definition.dart", "hash": "f0cf3060fe907fd075c49261e69b477c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/scale_rgba.dart", "hash": "c1f55f870549d1101d5387bab35567ba"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/visibility.dart", "hash": "94dab76e00a7b1155b15796b87ebe506"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/semantics/semantics.dart", "hash": "25c287ea52013abf7ffa061988242ad1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zip/zip_file.dart", "hash": "1382bc64a97b2cc89b01416b5dbd917e"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/tap_and_drag.dart", "hash": "a2f376b739fa28d7a71312ecf31d6465"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/extensions/find.dart", "hash": "17d3a0211da3d73d405d8730d46caacb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml_events/streams/normalizer.dart", "hash": "8bd96caadcaefb063cca0c83d7707a57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/analyzer.dart", "hash": "17aa54781ed25267f20b106de6b6d59a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/exr/exr_pxr24_compressor.dart", "hash": "e13de10e81aaa50cf8f8428b628dd5dd"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/spell_check_suggestions_toolbar.dart", "hash": "e4c4603e78131a8bc950a8029d624a76"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/binding.dart", "hash": "9c9f1e70fac06b3e87bb33ece047c4cf"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/scroll_simulation.dart", "hash": "b29e302994b1b0ea5029734406101b8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/whitespace.dart", "hash": "e63d3f21c1e3534e237fefbf5d3c2579"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/restoration_properties.dart", "hash": "a8fdf31698b305c9fdad63aa7a990766"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/autofill.dart", "hash": "4fa52a6cb3ac24b95e99a20d034f43c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/output_stream.dart", "hash": "3ca8f03581035097f3569def9d3fbd7f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "hash": "a79a6f9bb06c7d6dc5fb74ac53dce31b"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/inherited_notifier.dart", "hash": "12143f732513790cd579481704256dcd"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/flex.dart", "hash": "4ec9c8dd6d6ecb43d26ebaef03abd1ab"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/image_icon.dart", "hash": "2610f7ca2c31b37ad050671aafbccdd9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/core/result.dart", "hash": "6782f277d348804f26f7a748f647695a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/readers/schema_reader.dart", "hash": "c687a11b47bd8b6e262c4044fb107f5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/flutter_html.dart", "hash": "25b9c7ad4ae55a9aba74938520686974"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/dialog.dart", "hash": "3f3682db58f83007aada4d5c36376b90"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/search_anchor.dart", "hash": "873f01c9dae2d98c8df6fc08ca543aca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_with_context.dart", "hash": "a8f2c6aa382890a1bb34572bd2d264aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/util/output_buffer.dart", "hash": "a7f3899cb7f969aea6626ca5d9921e68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/webp/vp8l_bit_reader.dart", "hash": "827acad4176e61d3850183f1726c749f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/tiff/tiff_lzw_decoder.dart", "hash": "9e35a27f32aaf2130c86b23a6f1a04e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/file.dart", "hash": "dcef90946d14527736cde04a54d334db"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/table.dart", "hash": "eda0152837e3eb094d8b1f6d0754f088"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/lib/src/xml/nodes/element.dart", "hash": "361f3bd2e6ba6710885e241d7574326b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/util/point.dart", "hash": "bfcb1b8d041a74c451c0b46ddca26e7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/schema/opf/epub_metadata.dart", "hash": "68a5e4dc31b6c2c54ce6a651aa3ead16"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/color_scheme.dart", "hash": "7bbb6aab4e83fc272886a39c92157201"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/unicode_glyph_set.dart", "hash": "cdb411d670a094822c46ead81fc1c4f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/filter/contrast.dart", "hash": "7957161a3e8d8610b186b7fc10ede5cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/predicate/word.dart", "hash": "18e902c0d484a6a2e0d68837fc5f003d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/core/context.dart", "hash": "6f1cce384d53a00c3d6e036e78554066"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/segmented_control.dart", "hash": "8e58a1e955460cf5a4ea1cea2b7606cf"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/material.dart", "hash": "8ef67f192314481983c34c92a81ee5f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/formats/exr/exr_huffman.dart", "hash": "7717937484d202af0831dedb068fb2ab"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/cupertino/list_section.dart", "hash": "1363e5e6d5efab4bae027262eff73765"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/switch.dart", "hash": "1e840a2c03797a7468018e124b957d2f"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0/lib/src/utils.dart", "hash": "30cc9efa1930f42e94edba3c23aaffb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/crc32.dart", "hash": "52ebee94bf958053713e187f7904dca4"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "hash": "900a13c9fcd73f4e8e3d069d76af6ffa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0/lib/src/ref_entities/epub_chapter_ref.dart", "hash": "f4b062e5765e186b9bc6fbde4f694e67"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/painting/decoration_image.dart", "hash": "dd510cd97dc23d22aebc7b60affd6329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/src/exif/exif_value.dart", "hash": "cac07f16c9bb67ba52b8d0612c927e38"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/services/deferred_component.dart", "hash": "53b9028402187f878713225b48bdd5bb"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/services.dart", "hash": "0330f85971391a5f5457a20e933fe264"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/property.dart", "hash": "9f56fbe65bf797a2eba907e0181e69ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/misc/position.dart", "hash": "faedea5895c9ddd2b2c270817c61d1f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta.dart", "hash": "aaace37762c25bcd679c2ab09129db12"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/widgets/expansible.dart", "hash": "43bc92e2816a78f5d5987930bc3e804d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/gestures/force_press.dart", "hash": "d3de616e525e730c7b7e3beb57930993"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/lib/src/parser/character/predicate/whitespace.dart", "hash": "57a5a9f535e7c37d09bab9aca685dfd2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_buffers.dart", "hash": "4b495ff6681b3a7dda3f098bf9ecc77d"}, {"path": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "hash": "bda2eeb24233fd6f95dc5061b8bf3dd5"}]}