{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "isar_flutter_libs", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/isar_flutter_libs-3.1.0+1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "screen_brightness_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_ios-1.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "isar_flutter_libs", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/isar_flutter_libs-3.1.0+1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "screen_brightness_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_android-1.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "isar_flutter_libs", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/isar_flutter_libs-3.1.0+1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "screen_brightness_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_macos-1.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "isar_flutter_libs", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/isar_flutter_libs-3.1.0+1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "isar_flutter_libs", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/isar_flutter_libs-3.1.0+1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "screen_brightness_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_windows-1.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "fluttertoast", "dependencies": []}, {"name": "isar_flutter_libs", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "screen_brightness", "dependencies": ["screen_brightness_android", "screen_brightness_ios", "screen_brightness_macos", "screen_brightness_windows"]}, {"name": "screen_brightness_android", "dependencies": []}, {"name": "screen_brightness_ios", "dependencies": []}, {"name": "screen_brightness_macos", "dependencies": []}, {"name": "screen_brightness_windows", "dependencies": []}], "date_created": "2025-08-19 16:06:59.132863", "version": "3.32.0", "swift_package_manager_enabled": {"ios": false, "macos": false}}