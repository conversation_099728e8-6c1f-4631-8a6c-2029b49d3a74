{"logs": [{"outputFile": "com.example.my_flutter_app-mergeDebugResources-24:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/fed144bef681fc8fb80134095669b372/transformed/lifecycle-runtime-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "140", "startColumns": "4", "startOffsets": "6888", "endColumns": "42", "endOffsets": "6926"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/274b962bfc0ee3499460b07125b40653/transformed/jetified-savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "142", "startColumns": "4", "startOffsets": "6991", "endColumns": "53", "endOffsets": "7040"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e2fbf221633a11202a49e7e11d793bf1/transformed/jetified-activity-1.8.1/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "122,141", "startColumns": "4,4", "startOffsets": "5922,6931", "endColumns": "41,59", "endOffsets": "5959,6986"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/fe4c05eaffed710d6c61f97a8b8ac890/transformed/fragment-1.7.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "119,123,144,256,261", "startColumns": "4,4,4,4,4", "startOffsets": "5795,5964,7095,12410,12580", "endLines": "119,123,144,260,264", "endColumns": "56,64,63,24,24", "endOffsets": "5847,6024,7154,12575,12724"}}, {"source": "/Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/res/values/styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "173,818", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "476,982"}, "to": {"startLines": "155,159", "startColumns": "4,4", "startOffsets": "7894,8075", "endLines": "158,161", "endColumns": "12,12", "endOffsets": "8070,8239"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c7d3b67c81f17a9d01acb41ef7dc29fd/transformed/lifecycle-viewmodel-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "143", "startColumns": "4", "startOffsets": "7045", "endColumns": "49", "endOffsets": "7090"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/506e321e672d66bb2b4486d2ee8beed8/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "146", "startColumns": "4", "startOffsets": "7229", "endColumns": "82", "endOffsets": "7307"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "25,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,120,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,145,147,148,149,150,151,152,153,154,162,163,167,168,172,173,174,186,192,202,235,265,298", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "725,1723,1795,1883,1948,2014,2083,2146,2216,2284,2356,2426,2487,2561,2634,2695,2756,2818,2882,2944,3005,3073,3173,3233,3299,3372,3441,3498,3550,3612,3684,3760,3825,3884,3943,4003,4063,4123,4183,4243,4303,4363,4423,4483,4543,4602,4662,4722,4782,4842,4902,4962,5022,5082,5142,5202,5261,5321,5381,5440,5499,5558,5617,5676,5852,5887,6029,6084,6147,6202,6260,6318,6379,6442,6499,6550,6600,6661,6718,6784,6818,6853,7159,7312,7379,7451,7520,7589,7663,7735,7823,8244,8361,8562,8672,8873,9002,9074,9494,9697,9998,11729,12729,13411", "endLines": "25,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,120,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,145,147,148,149,150,151,152,153,154,162,166,167,171,172,173,174,191,201,234,255,297,303", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "780,1790,1878,1943,2009,2078,2141,2211,2279,2351,2421,2482,2556,2629,2690,2751,2813,2877,2939,3000,3068,3168,3228,3294,3367,3436,3493,3545,3607,3679,3755,3820,3879,3938,3998,4058,4118,4178,4238,4298,4358,4418,4478,4538,4597,4657,4717,4777,4837,4897,4957,5017,5077,5137,5197,5256,5316,5376,5435,5494,5553,5612,5671,5730,5882,5917,6079,6142,6197,6255,6313,6374,6437,6494,6545,6595,6656,6713,6779,6813,6848,6883,7224,7374,7446,7515,7584,7658,7730,7818,7889,8356,8557,8667,8868,8997,9069,9136,9692,9993,11724,12405,13406,13573"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/res/values/values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,3,9,17,26,38,44,50,51,52,53,54,118,175,181,304,312,327", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,785,1099,1287,1474,1527,1587,1639,1684,5735,9141,9336,13578,13860,14474", "endLines": "2,8,16,24,37,43,49,50,51,52,53,54,118,180,185,311,326,342", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1094,1282,1469,1522,1582,1634,1679,1718,5790,9331,9489,13855,14469,15123"}}]}]}