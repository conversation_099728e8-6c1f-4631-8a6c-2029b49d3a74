-- Merging decision tree log ---
application
INJECTED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:7:5-41:19
INJECTED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/debug/AndroidManifest.xml
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/506e321e672d66bb2b4486d2ee8beed8/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/506e321e672d66bb2b4486d2ee8beed8/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/a57044d90e87c68614d167bac9b8275a/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/a57044d90e87c68614d167bac9b8275a/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:name
		INJECTED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml
manifest
ADDED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:1:1-53:12
MERGED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:1:1-53:12
INJECTED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:path_provider_android] /Users/<USER>/Desktop/test/my_flutter_app/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/fe4c05eaffed710d6c61f97a8b8ac890/transformed/fragment-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/88ea6bbee0c3d252da1a135eb4f3ca19/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:17:1-21:12
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/e2fbf221633a11202a49e7e11d793bf1/transformed/jetified-activity-1.8.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/669dcb043dfd5b8ba3c17c25f2994f66/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1b073446a44c5a5ccccc7d6163478c48/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/661cfed71eb01d7f0c4bbaeae6faab4d/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc9487afb3090e92844f4382c0195afd/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c7d3b67c81f17a9d01acb41ef7dc29fd/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/2e82a987f49d0f6484c5e02710942378/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/51aab5cd0fa4197e7c50f04a365e85eb/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/d6a04692cb28023b65a80b358730356b/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/96ab1378dc0dd3297172010fa8b1001f/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/fed144bef681fc8fb80134095669b372/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/274b962bfc0ee3499460b07125b40653/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/506e321e672d66bb2b4486d2ee8beed8/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/dcb95f8119ec5b1845af3693aa8a7063/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/9724ff6e8d4e6c9244bfb7005fae21c2/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/a57044d90e87c68614d167bac9b8275a/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/54366e878a08f39217fdd8161987674f/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/40dfcd36206381321d61bc4d0a504790/transformed/jetified-core-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/fe74d444a12af678d91d4bc25f88f447/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/6ebb3aa7827752119b2d0074c8f4eeaa/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:2:1-7:12
	package
		INJECTED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/debug/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:2:5-51
		ADDED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:5:5-67
MERGED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:5:5-67
MERGED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:5:5-67
	android:name
		ADDED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:5:22-64
queries
ADDED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:47:5-52:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:48:9-51:18
action#android.intent.action.PROCESS_TEXT
ADDED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:49:13-72
	android:name
		ADDED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:49:21-70
data
ADDED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:50:13-50
	android:mimeType
		ADDED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/main/AndroidManifest.xml:50:19-48
uses-sdk
INJECTED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/debug/AndroidManifest.xml
MERGED from [:path_provider_android] /Users/<USER>/Desktop/test/my_flutter_app/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Desktop/test/my_flutter_app/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/fe4c05eaffed710d6c61f97a8b8ac890/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/fe4c05eaffed710d6c61f97a8b8ac890/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/88ea6bbee0c3d252da1a135eb4f3ca19/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/88ea6bbee0c3d252da1a135eb4f3ca19/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/e2fbf221633a11202a49e7e11d793bf1/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/e2fbf221633a11202a49e7e11d793bf1/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/669dcb043dfd5b8ba3c17c25f2994f66/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/669dcb043dfd5b8ba3c17c25f2994f66/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1b073446a44c5a5ccccc7d6163478c48/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1b073446a44c5a5ccccc7d6163478c48/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/661cfed71eb01d7f0c4bbaeae6faab4d/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/661cfed71eb01d7f0c4bbaeae6faab4d/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc9487afb3090e92844f4382c0195afd/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc9487afb3090e92844f4382c0195afd/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c7d3b67c81f17a9d01acb41ef7dc29fd/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c7d3b67c81f17a9d01acb41ef7dc29fd/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/2e82a987f49d0f6484c5e02710942378/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/2e82a987f49d0f6484c5e02710942378/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/51aab5cd0fa4197e7c50f04a365e85eb/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/51aab5cd0fa4197e7c50f04a365e85eb/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/d6a04692cb28023b65a80b358730356b/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/d6a04692cb28023b65a80b358730356b/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/96ab1378dc0dd3297172010fa8b1001f/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/96ab1378dc0dd3297172010fa8b1001f/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/fed144bef681fc8fb80134095669b372/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/fed144bef681fc8fb80134095669b372/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/274b962bfc0ee3499460b07125b40653/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/274b962bfc0ee3499460b07125b40653/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/506e321e672d66bb2b4486d2ee8beed8/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/506e321e672d66bb2b4486d2ee8beed8/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/dcb95f8119ec5b1845af3693aa8a7063/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/dcb95f8119ec5b1845af3693aa8a7063/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/9724ff6e8d4e6c9244bfb7005fae21c2/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/9724ff6e8d4e6c9244bfb7005fae21c2/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/a57044d90e87c68614d167bac9b8275a/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/a57044d90e87c68614d167bac9b8275a/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/54366e878a08f39217fdd8161987674f/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/54366e878a08f39217fdd8161987674f/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/40dfcd36206381321d61bc4d0a504790/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/40dfcd36206381321d61bc4d0a504790/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/fe74d444a12af678d91d4bc25f88f447/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/fe74d444a12af678d91d4bc25f88f447/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/6ebb3aa7827752119b2d0074c8f4eeaa/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/6ebb3aa7827752119b2d0074c8f4eeaa/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Desktop/test/my_flutter_app/android/app/src/debug/AndroidManifest.xml
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/26c99be856553367d8fad52c95155b00/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.example.my_flutter_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.example.my_flutter_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/73aa083afefb941d18007d1b70cec6be/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/506e321e672d66bb2b4486d2ee8beed8/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/506e321e672d66bb2b4486d2ee8beed8/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c26e9bd9e94c83e6b65abe3b819ef578/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/85792c7efa6a1c8df0b33488ef129b4a/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
