{"configVersion": 2, "packages": [{"name": "archive", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "boolean_selector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "characters", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "clock", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cosmos_epub", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cosmos_epub-0.0.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "crypto", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "csslib", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-0.17.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "cupertino_icons", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "epubx", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/epubx-4.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "fading_edge_scrollview", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fading_edge_scrollview-4.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "fake_async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter", "rootUri": "file:///Users/<USER>/fvm/versions/3.32.0/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_html_reborn", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html_reborn-3.0.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-6.0.0", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "flutter_screenutil", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_test", "rootUri": "file:///Users/<USER>/fvm/versions/3.32.0/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_web_plugins", "rootUri": "file:///Users/<USER>/fvm/versions/3.32.0/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "fluttertoast", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "get", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/get-4.7.2", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "get_storage", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/get_storage-2.1.1", "packageUri": "lib/", "languageVersion": "2.16"}, {"name": "html", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.4", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "http", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_parser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "isar", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/isar-3.1.0+1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "isar_flutter_libs", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/isar_flutter_libs-3.1.0+1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "js", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "leak_tracker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-6.0.0", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "list_counter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/list_counter-1.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "matcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "path_provider_foundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "path_provider_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "petitparser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "platform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "quiver", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "screen_brightness", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness-1.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "screen_brightness_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_android-1.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "screen_brightness_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_ios-1.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "screen_brightness_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_macos-1.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "screen_brightness_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_platform_interface-1.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "screen_brightness_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_windows-1.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "sky_engine", "rootUri": "file:///Users/<USER>/fvm/versions/3.32.0/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "source_span", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "stack_trace", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "string_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "term_glyph", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test_api", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "typed_data", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "vector_math", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "xdg_directories", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "my_flutter_app", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.8"}], "generator": "pub", "generatorVersion": "3.8.0", "flutterRoot": "file:///Users/<USER>/fvm/versions/3.32.0", "flutterVersion": "3.32.0", "pubCache": "file:///Users/<USER>/.pub-cache"}