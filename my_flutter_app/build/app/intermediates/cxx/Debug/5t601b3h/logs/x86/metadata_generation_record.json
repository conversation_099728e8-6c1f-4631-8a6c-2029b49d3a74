[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: x86", "file_": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON '/Users/<USER>/Desktop/test/my_flutter_app/build/.cxx/Debug/5t601b3h/x86/android_gradle_build.json' was up-to-date", "file_": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/fvm/versions/3.32.0/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]