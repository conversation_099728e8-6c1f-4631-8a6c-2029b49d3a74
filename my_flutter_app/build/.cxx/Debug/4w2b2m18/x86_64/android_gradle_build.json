{"buildFiles": ["/Users/<USER>/fvm/versions/3.32.0/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/test/my_flutter_app/build/.cxx/Debug/4w2b2m18/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/test/my_flutter_app/build/.cxx/Debug/4w2b2m18/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": []}